name: 🔀 Sync mirror to <PERSON><PERSON><PERSON>

on:
  push:
    branches:
      - master
      - dev
  create:

jobs:
  mirror:
    runs-on: ubuntu-latest
    if: github.repository == 'jumpserver/jumpserver'
    steps:
      - name: mirror
        continue-on-error: true
        if: github.event_name == 'push' || (github.event_name == 'create' && github.event.ref_type == 'tag')
        uses: wearerequired/git-mirror-action@v1
        env:
          SSH_PRIVATE_KEY: ${{ secrets.GITEE_SSH_PRIVATE_KEY }}
        with:
          source-repo: '**************:jumpserver/jumpserver.git'
          destination-repo: '*************:fit2cloud-feizhiyun/JumpServer.git'
