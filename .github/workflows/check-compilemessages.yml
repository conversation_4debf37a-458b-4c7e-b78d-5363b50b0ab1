name: Check I18n files CompileMessages

on:
  pull_request:
    branches:
      - 'dev'
    paths:
      - 'apps/i18n/core/**/*.po'
    types:
      - opened
      - synchronize
      - reopened
jobs:
  compile-messages-check:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build and check compilemessages
        uses: docker/build-push-action@v6
        with:
          platforms: linux/amd64
          push: false
          file: Dockerfile
          target: stage-build
          tags: jumpserver/core:stage-build
