name: LLM Code Review

permissions:
  contents: read
  pull-requests: write

on:
  pull_request:
    types: [opened, reopened, synchronize]

jobs:
  llm-code-review:
    runs-on: ubuntu-latest
    steps:
      - uses: fit2cloud/LLM-CodeReview-Action@main
        env:
          GITHUB_TOKEN: ${{ secrets.FIT2CLOUDRD_LLM_CODE_REVIEW_TOKEN }}
          OPENAI_API_KEY: ${{ secrets.ALIYUN_LLM_API_KEY }}
          LANGUAGE: English
          OPENAI_API_ENDPOINT: https://dashscope.aliyuncs.com/compatible-mode/v1
          MODEL: qwen2-1.5b-instruct
          PROMPT: "Please check the following code differences for any irregularities, potential issues, or optimization suggestions, and provide your answers in English."
          top_p: 1
          temperature: 1
          # max_tokens: 10000
          MAX_PATCH_LENGTH: 10000 
          IGNORE_PATTERNS: "/node_modules,*.md,/dist,/.github"
          FILE_PATTERNS: "*.java,*.go,*.py,*.vue,*.ts,*.js,*.css,*.scss,*.html"
