name: "Run Build Test"
on:
  push:
    paths:
      - 'Dockerfile'
      - 'Dockerfile*'
      - 'Dockerfile-*'
      - 'pyproject.toml'
      - 'poetry.lock'

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        component: [core]
        version: [v4]
    steps:
    - uses: actions/checkout@v4
    - uses: docker/setup-buildx-action@v3

    - name: Login to GitHub Container Registry
      uses: docker/login-action@v3
      with:
        registry: ghcr.io
        username: ${{ github.repository_owner }}
        password: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Prepare Build
      run: |
        sed -i 's@^FROM registry.fit2cloud.com/jumpserver@FROM ghcr.io/jumpserver@g' Dockerfile-ee

    - name: Build CE Image
      uses: docker/build-push-action@v5
      with:
        context: .
        push: true
        file: Dockerfile
        tags: ghcr.io/jumpserver/${{ matrix.component }}:${{ matrix.version }}-ce
        platforms: linux/amd64
        build-args: |
          VERSION=${{ matrix.version }}
          APT_MIRROR=http://deb.debian.org
          PIP_MIRROR=https://pypi.org/simple
        outputs: type=image,oci-mediatypes=true,compression=zstd,compression-level=3,force-compression=true
        cache-from: type=gha
        cache-to: type=gha,mode=max

    - name: Build EE Image
      uses: docker/build-push-action@v5
      with:
        context: .
        push: false
        file: Dockerfile-ee
        tags: ghcr.io/jumpserver/${{ matrix.component }}:${{ matrix.version }}
        platforms: linux/amd64
        build-args: |
          VERSION=${{ matrix.version }}
          APT_MIRROR=http://deb.debian.org
          PIP_MIRROR=https://pypi.org/simple
        outputs: type=image,oci-mediatypes=true,compression=zstd,compression-level=3,force-compression=true
        cache-from: type=gha
        cache-to: type=gha,mode=max