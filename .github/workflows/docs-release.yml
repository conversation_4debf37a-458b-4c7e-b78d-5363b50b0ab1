name: Auto update docs changelog

on:
  release:
    types: [published]

jobs:
  update_docs_changelog:
    runs-on: ubuntu-latest
    if: startsWith(github.event.release.tag_name, 'v4.')
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Update docs changelog
        env:
          TAG_NAME: ${{ github.event.release.tag_name }}
          DOCS_TOKEN: ${{ secrets.DOCS_TOKEN }}
        run: |
          git config --global user.name 'Bai<PERSON>iang<PERSON><PERSON>'
          git config --global user.email '<EMAIL>'

          git clone https://$<EMAIL>/jumpservice/documentation.git
          cd documentation/utils
          bash update_changelog.sh