name: '🐛 Bug Report'
description: 'Report an Bug'
title: '[Bug] '
labels: ['🐛 Bug']
assignees: 
  - baijiangjie
body:
  - type: input
    attributes:
      label: 'Product Version'
      description: The versions prior to v2.28 (inclusive) are no longer supported.
    validations:
      required: true

  - type: checkboxes
    attributes:
      label: 'Product Edition'
      options:
        - label: 'Community Edition'
        - label: 'Enterprise Edition'
        - label: 'Enterprise Trial Edition'
    validations:
      required: true
    
  - type: checkboxes
    attributes:
      label: 'Installation Method'
      options:
        - label: 'Online Installation (One-click command installation)'
        - label: 'Offline Package Installation'
        - label: 'All-in-One'
        - label: '1Panel'
        - label: 'Kubernetes'
        - label: 'Source Code'

  - type: textarea
    attributes:
      label: 'Environment Information'
      description: Please provide a clear and concise description outlining your environment information.
    validations:
      required: true

  - type: textarea
    attributes:
      label: '🐛 Bug Description'
      description:
        Please provide a clear and concise description of the defect. If the issue is complex, please provide detailed explanations. <br/>
        Unclear descriptions will not be processed. Please ensure you provide enough detail and information to support replicating and fixing the defect.
    validations:
      required: true

  - type: textarea
    attributes:
      label: 'Recurrence Steps'
      description: Please provide a clear and concise description outlining how to reproduce the issue.
    validations:
      required: true

  - type: textarea
    attributes:
      label: 'Expected Behavior'
      description: Please provide a clear and concise description of what you expect to happen.

  - type: textarea
    attributes:
      label: 'Additional Information'
      description: Please add any additional background information about the issue here.

  - type: textarea
    attributes:
      label: 'Attempted Solutions'
      description: If you have already attempted to solve the issue, please list the solutions you have tried here.
