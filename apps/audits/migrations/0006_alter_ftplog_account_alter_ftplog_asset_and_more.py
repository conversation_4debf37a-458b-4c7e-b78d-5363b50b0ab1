# Generated by Django 4.1.13 on 2025-04-21 06:15

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ('audits', '0005_rename_serviceaccesslog'),
    ]

    operations = [
        migrations.AlterField(
            model_name='ftplog',
            name='account',
            field=models.Char<PERSON>ield(db_index=True, max_length=128, verbose_name='Account'),
        ),
        migrations.AlterField(
            model_name='ftplog',
            name='asset',
            field=models.CharField(db_index=True, max_length=767, verbose_name='Asset'),
        ),
        migrations.AlterField(
            model_name='ftplog',
            name='date_start',
            field=models.DateTimeField(auto_now_add=True, verbose_name='Date start'),
        ),
        migrations.AddIndex(
            model_name='ftplog',
            index=models.Index(fields=['date_start', 'org_id'], name='idx_date_start_org'),
        ),
    ]
