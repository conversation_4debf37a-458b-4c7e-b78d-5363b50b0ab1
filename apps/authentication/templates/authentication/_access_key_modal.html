{% extends '_modal.html' %}
{% load i18n %}
{% load static %}
{% block modal_id %}access_key_modal{% endblock %}
{% block modal_class %}modal-lg{% endblock %}
{% block modal_title%}{% trans "API key list" %}{% endblock %}
{% block modal_body %}
<style>
    .inmodal .modal-body {
        background: #fff;
    }
    #access_key_list_table_wrapper {
        padding-top: 10px;
    }

</style>
<div class="alert alert-info help-message" style="margin-left: 0px; margin-right: 0px;">
    {% trans 'Using api key sign api header, every requests header difference'%}
    <a href="https://jumpserver.readthedocs.io/zh/master/api_auth.html#access-key" target="_blank">{% trans 'docs' %} </a>
</div>
<div class="uc pull-left m-r-0 m-t-10">
    <button class="btn btn-primary btn-sm" id="create-btn" href="#"> {% trans "Create" %} </button>
</div>
<table class="table table-striped table-bordered table-hover " id="access_key_list_table" style="width: 100%">
    <thead>
    <tr>
        <th class="text-center">
            <input type="checkbox" id="check_all" class="ipt_check_all" >
        </th>
        <th class="text-center">{% trans 'ID' %}</th>
        <th class="text-center">{% trans 'Secret' %}</th>
        <th class="text-center">{% trans 'Active' %}</th>
        <th class="text-center">{% trans 'Date' %}</th>
        <th class="text-center">{% trans 'Action' %}</th>
    </tr>
    </thead>
    <tbody>
    </tbody>
</table>

<script>
var ak_table = null;
function initAccessKeyTable() {
    var options = {
        ele: $('#access_key_list_table'),
        columnDefs: [
            {targets: 2, createdCell: function (td, cellData) {
                var btn = '<button class="btn btn-primary btn-xs btn-secret" data-secret="SECRET">{% trans 'Show' %}</button>';
                btn = btn.replace("SECRET", cellData);
                $(td).html(btn)
            }},
            {targets: 3, createdCell: function (td, cellData) {
               if (cellData) {
                    $(td).html('<i class="fa fa-check text-navy"></i>')
                } else {
                   $(td).html('<i class="fa fa-times text-danger"></i>')
                }
            }},
            {targets: 4, createdCell: function (td, cellData) {
                var date = toSafeLocalDateStr(cellData);
                $(td).html(date)
            }},
            {targets: 5, createdCell: function (td, cellData, rowData) {
                var btn = '';
                var btn_del = '<a class="btn btn-xs btn-danger m-l-xs btn-api-keydel" data-id="ID">{% trans "Delete" %}</a>';
                var btn_inactive = '<a class="btn btn-xs btn-info m-l-xs btn-inactive" data-id="ID">{% trans "Disable" %}</a>';
                var btn_active = '<a class="btn btn-xs btn-primary m-l-xs btn-active" data-id="ID">{% trans "Enable" %}</a>';

                btn += btn_del;
                if (rowData.is_active) {
                    btn += btn_inactive
                } else {
                    btn += btn_active
                }
                btn = btn.replaceAll("ID", cellData);
                $(td).html(btn);
            }}
        ],
        ajax_url: '{% url "api-auth:access-key-list" %}',
        columns: [
            {data: "id"},
            {data: "id"},
            {data: "secret"},
            {data: "is_active"},
            {data: "date_created"},
            {data: "id", orderable: false}
        ],
    };
    ak_table = jumpserver.initServerSideDataTable(options);
}

$(document).ready(function () {
}).on("show.bs.modal", "#access_key_modal", function () {
    if (!ak_table) {
        initAccessKeyTable()
    }
}).on("click", "#create-btn", function () {
    var url = "{% url "api-auth:access-key-list" %}";
    var data = {
        url: url,
        method: 'POST',
        success: function () {
            ak_table.ajax.reload();
        }
    };
    requestApi(data)
}).on("click", ".btn-secret", function () {
    var $this = $(this);
    $this.parent().html($this.data("secret"))
}).on("click", ".btn-api-key-del", function () {
    var url = "{% url "api-auth:access-key-detail" pk=DEFAULT_PK %}";
    url = url.replace("{{ DEFAULT_PK }}", $(this).data("id")) ;
    objectDelete($(this), $(this).data("id"), url);
}).on("click", ".btn-active", function () {
    var url = "{% url "api-auth:access-key-detail" pk=DEFAULT_PK %}";
    url = url.replace("{{ DEFAULT_PK }}", $(this).data("id")) ;
    var data = {
        url: url,
        body: JSON.stringify({"is_active": true}),
        method: "PATCH",
        success: function () {
            ak_table.ajax.reload();
        }
    };
    requestApi(data)
}).on("click", ".btn-inactive", function () {
    var url = "{% url "api-auth:access-key-detail" pk=DEFAULT_PK %}";
    url = url.replace("{{ DEFAULT_PK }}", $(this).data("id")) ;
    var data = {
        url: url,
        body: JSON.stringify({"is_active": false}),
        method: "PATCH",
        success: function () {
            ak_table.ajax.reload();
        }
    };
    requestApi(data)
}).on('click', '.btn-api-keydel', function (){
    var url = "{% url "api-auth:access-key-detail" pk=DEFAULT_PK %}";
    url = url.replace("{{ DEFAULT_PK }}", $(this).data("id")) ;
    var data = {
        url: url,
        method: "DELETE",
        success: function () {
            ak_table.ajax.reload();
        },
        success_message: "{% trans 'Delete success' %}"

    };
    requestApi(data)
})
</script>
{% endblock %}
{% block modal_button %}
  <button data-dismiss="modal" class="btn btn-white close_btn2" type="button">{% trans "Close" %}</button>
{% endblock %}
