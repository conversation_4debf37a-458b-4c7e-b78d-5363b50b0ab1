# Generated by Django 4.1.13 on 2024-11-11 11:04

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('authentication', '0005_connectiontoken_face_monitor_token'),
    ]

    operations = [
        migrations.AddField(
            model_name='connectiontoken',
            name='type',
            field=models.CharField(choices=[('admin', 'Admin'), ('super', 'Super'), ('user', 'User')], default='user', max_length=16, verbose_name='Type'),
        ),
        migrations.CreateModel(
            name='AdminConnectionToken',
            fields=[
            ],
            options={
                'verbose_name': 'Admin connection token',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('authentication.connectiontoken',),
        ),
    ]
