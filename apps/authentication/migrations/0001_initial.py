# Generated by Django 4.1.13 on 2024-05-09 03:16

import uuid

from django.db import migrations, models

import authentication.models.access_key
import authentication.models.connection_token
import common.db.fields


class Migration(migrations.Migration):
    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='AccessKey',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False,
                                        verbose_name='AccessKeyID')),
                ('secret', models.CharField(default=authentication.models.default_secret, max_length=36,
                                            verbose_name='AccessKeySecret')),
                ('ip_group', models.JSONField(default=authentication.models.default_ip_group, verbose_name='IP group')),
                ('is_active', models.BooleanField(default=True, verbose_name='Active')),
                ('date_last_used', models.DateTimeField(blank=True, null=True, verbose_name='Date last used')),
                ('date_created', models.DateTime<PERSON>ield(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'Access key',
            },
        ),
        migrations.CreateModel(
            name='ConnectionToken',
            fields=[
                ('created_by', models.CharField(blank=True, max_length=128, null=True, verbose_name='Created by')),
                ('updated_by', models.CharField(blank=True, max_length=128, null=True, verbose_name='Updated by')),
                ('date_created', models.DateTimeField(auto_now_add=True, null=True, verbose_name='Date created')),
                ('date_updated', models.DateTimeField(auto_now=True, verbose_name='Date updated')),
                ('comment', models.TextField(blank=True, default='', verbose_name='Comment')),
                ('id', models.UUIDField(default=uuid.uuid4, primary_key=True, serialize=False)),
                ('org_id',
                 models.CharField(blank=True, db_index=True, default='', max_length=36, verbose_name='Organization')),
                ('value', models.CharField(default='', max_length=64, verbose_name='Value')),
                ('account', models.CharField(max_length=128, verbose_name='Account name')),
                ('input_username',
                 models.CharField(blank=True, default='', max_length=128, verbose_name='Input username')),
                ('input_secret',
                 common.db.fields.EncryptTextField(blank=True, default='', max_length=64, verbose_name='Input secret')),
                ('protocol', models.CharField(default='ssh', max_length=16, verbose_name='Protocol')),
                ('connect_method', models.CharField(max_length=32, verbose_name='Connect method')),
                ('connect_options', models.JSONField(default=dict, verbose_name='Connect options')),
                ('user_display', models.CharField(default='', max_length=128, verbose_name='User display')),
                ('asset_display', models.CharField(default='', max_length=128, verbose_name='Asset display')),
                ('is_reusable', models.BooleanField(default=False, verbose_name='Reusable')),
                ('date_expired',
                 models.DateTimeField(default=authentication.models.connection_token.date_expired_default,
                                      verbose_name='Date expired')),
                ('is_active', models.BooleanField(default=True, verbose_name='Active')),
            ],
            options={
                'verbose_name': 'Connection token',
                'ordering': ('-date_expired',),
                'permissions': [('expire_connectiontoken', 'Can expire connection token'),
                                ('reuse_connectiontoken', 'Can reuse connection token')],
            },
        ),
        migrations.CreateModel(
            name='Passkey',
            fields=[
                ('created_by', models.CharField(blank=True, max_length=128, null=True, verbose_name='Created by')),
                ('updated_by', models.CharField(blank=True, max_length=128, null=True, verbose_name='Updated by')),
                ('date_created', models.DateTimeField(auto_now_add=True, null=True, verbose_name='Date created')),
                ('date_updated', models.DateTimeField(auto_now=True, verbose_name='Date updated')),
                ('comment', models.TextField(blank=True, default='', verbose_name='Comment')),
                ('id', models.UUIDField(default=uuid.uuid4, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=255, verbose_name='Name')),
                ('is_active', models.BooleanField(default=True, verbose_name='Enabled')),
                ('platform', models.CharField(default='', max_length=255, verbose_name='Platform')),
                ('added_on', models.DateTimeField(auto_now_add=True, verbose_name='Added on')),
                ('date_last_used', models.DateTimeField(default=None, null=True, verbose_name='Date last used')),
                ('credential_id', models.CharField(max_length=255, unique=True, verbose_name='Credential ID')),
                ('token', models.CharField(max_length=255, verbose_name='Token')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='PrivateToken',
            fields=[
                ('key', models.CharField(max_length=40, primary_key=True, serialize=False, verbose_name='Key')),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created')),
                ('date_last_used', models.DateTimeField(blank=True, null=True, verbose_name='Date last used')),
            ],
            options={
                'verbose_name': 'Private Token',
            },
        ),
        migrations.CreateModel(
            name='SSOToken',
            fields=[
                ('created_by', models.CharField(blank=True, max_length=128, null=True, verbose_name='Created by')),
                ('updated_by', models.CharField(blank=True, max_length=128, null=True, verbose_name='Updated by')),
                ('date_created', models.DateTimeField(auto_now_add=True, null=True, verbose_name='Date created')),
                ('date_updated', models.DateTimeField(auto_now=True, verbose_name='Date updated')),
                ('comment', models.TextField(blank=True, default='', verbose_name='Comment')),
                ('authkey',
                 models.UUIDField(default=uuid.uuid4, primary_key=True, serialize=False, verbose_name='Token')),
                ('expired', models.BooleanField(default=False, verbose_name='Expired')),
            ],
            options={
                'verbose_name': 'SSO token',
            },
        ),
        migrations.CreateModel(
            name='TempToken',
            fields=[
                ('created_by', models.CharField(blank=True, max_length=128, null=True, verbose_name='Created by')),
                ('updated_by', models.CharField(blank=True, max_length=128, null=True, verbose_name='Updated by')),
                ('date_created', models.DateTimeField(auto_now_add=True, null=True, verbose_name='Date created')),
                ('date_updated', models.DateTimeField(auto_now=True, verbose_name='Date updated')),
                ('comment', models.TextField(blank=True, default='', verbose_name='Comment')),
                ('id', models.UUIDField(default=uuid.uuid4, primary_key=True, serialize=False)),
                ('username', models.CharField(max_length=128, verbose_name='Username')),
                ('secret', models.CharField(max_length=64, verbose_name='Secret')),
                ('verified', models.BooleanField(default=False, verbose_name='Verified')),
                ('date_verified', models.DateTimeField(null=True, verbose_name='Date verified')),
                ('date_expired', models.DateTimeField(verbose_name='Date expired')),
            ],
            options={
                'verbose_name': 'Temporary token',
            },
        ),
    ]
