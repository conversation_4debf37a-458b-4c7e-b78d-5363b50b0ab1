# 使用方法

## 1. 概要

本 API は PAM 資産アカウントサービスの表示を提供し、RESTful スタイルの呼び出しをサポートし、データは JSON 形式で返されます。

## 2. 環境要件

- `Java 8+`
- `HttpClient`

## 3. 使用方法

**リクエスト方法**: `GET api/v1/accounts/integration-applications/account-secret/`

**リクエストパラメータ**

| パラメータ名 | タイプ | 必須 | 説明            |
|-------------|------|----|----------------|
| asset       | str  | はい | 資産 ID / 資産名 |
| account     | str  | はい | アカウント ID / アカウント名 |

**レスポンス例**:
```json
{
    "id": "72b0b0aa-ad82-4182-a631-ae4865e8ae0e", 
    "secret": "123456"
}
```

## よくある質問（FAQ）

Q: APIキーはどのように取得しますか？

A: PAM - アプリケーション管理でアプリケーションを作成し、KEY_IDとKEY_SECRETを生成できます。

## バージョン履歴（Changelog）


| バージョン | 変更内容              | 日付         |
| -------- | ----------------- |------------|
| 1.0.0    | 初期バージョン          | 2025-02-11 |
