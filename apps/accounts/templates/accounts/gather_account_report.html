{% load i18n %} {% load static %}

<div class="report-container">
  <section class="basic-info-section">
    <div class="header-container">
      <span
        class="triangle-down-icon collapse-toggle"
        data-target="basic-info"
      ></span>

      <h2>{% trans 'Basic Information' %}</h2>
    </div>

    <div class="container-section collapsible-content" id="basic-info-content">
      <div class="item">
        <span class="item-label">{% trans 'Task name' %}</span>
        <span class="item-value">{{ execution.snapshot.name }}</span>
      </div>

      <div class="item">
        <span class="item-label">{% trans 'Date start' %}</span>
        <span class="item-value">{{ execution.date_start | date:"Y/m/d H:i:s" }}</span>
      </div>

      <div class="item">
        <span class="item-label">{% trans 'Date end' %}</span>
        <span class="item-value">{{ execution.date_finished | date:"Y/m/d H:i:s" }}</span>
      </div>

      <div class="item">
        <span class="item-label">{% trans 'Assets amount' %}</span>
        <span class="item-value">{{ summary.total_assets | default:0 }}</span>
      </div>

      <div class="item">
        <span class="item-label">{% trans 'Asset success count' %}</span>
        <span class="item-value">{{ summary.ok_assets | default:0 }}</span>
      </div>

      <div class="item">
        <span class="item-label">{% trans 'Asset failed count' %}</span>
        <span class="item-value">{{ summary.fail_assets | default:0 }}</span>
      </div>

      <div class="item">
        <span class="item-label">{% trans 'Asset not support count' %}</span>
        <span class="item-value">{{ summary.error_assets | default:0 }}</span>
      </div>

      <div class="item">
        <span class="item-label">{% trans 'Time using' %}</span>
        <span class="item-value">{{ execution.duration }}s</span>
      </div>
    </div>
  </section>

  <section class="success">
    <div class="header-container">
      <span
        class="triangle-down-icon collapse-toggle"
        data-target="success"
      ></span>

      <h2>
        {% trans 'New found accounts' %}:

        <span> {{ summary.new_accounts | default:0 }} </span>
      </h2>
    </div>

    <div class="container-section collapsible-content" id="success-content">
      {% if summary.new_accounts %}
      <table class="custom-table table-striped table-hover data-table">
        <thead>
          <tr>
            <th width="10%">No.</th>
            <th width="45%">{% trans 'Asset' %}</th>
            <th width="45%">{% trans 'Username' %}</th>
          </tr>
        </thead>
        <tbody>
          {% for account in result.new_accounts %}
          <tr>
            <td>{{ forloop.counter }}</td>
            <td>{{ account.asset }}</td>
            <td>{{ account.username }}</td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
      {% else %}
      <p class="no-data">{% trans 'No new accounts found' %}</p>
      {% endif %}
    </div>
  </section>

  <section class="failed">
    <div class="header-container">
      <span
        class="triangle-down-icon collapse-toggle"
        data-target="failed"
      ></span>

      <h2>
        {% trans 'Lost accounts' %}:

        <span> {{ summary.lost_accounts | default:0 }} </span>
      </h2>
    </div>

    <div class="container-section collapsible-content" id="failed-content">
      {% if summary.lost_accounts %}
      <table class="custom-table table-hover data-table">
        <thead>
          <tr>
            <th width="10%">No.</th>
            <th width="45%">{% trans 'Asset' %}</th>
            <th width="45%">{% trans 'Username' %}</th>
          </tr>
        </thead>
        <tbody>
          {% for account in result.lost_accounts %}
          <tr>
            <td>{{ forloop.counter }}</td>
            <td>{{ account.asset }}</td>
            <td>{{ account.username }}</td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
      {% else %}
      <p class="no-data">{% trans 'No lost accounts found' %}</p>
      {% endif %}
    </div>
  </section>
</div>

<script>
  const toggleButtons = document.querySelectorAll('.collapse-toggle')

  toggleButtons.forEach((button) => {
    button.addEventListener('click', function () {
      const targetId = this.getAttribute('data-target')
      const targetContent = document.getElementById(`${targetId}-content`)

      if (this.classList.contains('triangle-down-icon')) {
        this.classList.remove('triangle-down-icon')
        this.classList.add('triangle-right-icon')
        targetContent.classList.add('collapsed')
      } else {
        this.classList.remove('triangle-right-icon')
        this.classList.add('triangle-down-icon')
        targetContent.classList.remove('collapsed')
      }
    })
  })
</script>

<style>
    html {
        font-size: 10px;
    }

    body,
    p {
        margin: 0;
        padding: 0;
    }

    .report-container {
        display: flex;
        flex-direction: column;
        row-gap: 16px;
    }

    .basic-info-section,
    .success,
    .failed {
        display: flex;
        flex-direction: column;
        height: 100%;
        padding: 16px 20px;
        margin: 0 20px;
        border: 1px solid #dee0e3;
        border-radius: 6px;
        background-color: #fff;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        transition: all 0.2s ease;
    }

    .header-container {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        height: 32px;
        margin-bottom: 12px;
        border-bottom: 1px solid #f0f0f0;
        padding-bottom: 8px;
    }

    .header-container h2 {
        color: #1f2329;
        font: 500 16px/24px 'PingFang SC';
        margin: 0;
        display: flex;
        align-items: center;
        width: 100%;
    }

    .header-container h2 span {
        display: inline-flex;
        margin-left: 8px;
        font-weight: normal;
    }

    .triangle-right-icon {
        display: inline-block;
        width: 0;
        height: 0;
        border-top: 5px solid transparent;
        border-bottom: 5px solid transparent;
        border-left: 8px solid #1f2329;
        margin-right: 10px;
        cursor: pointer;
    }

    .triangle-down-icon {
        display: inline-block;
        width: 0;
        height: 0;
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-top: 8px solid #1f2329;
        margin-right: 10px;
        cursor: pointer;
    }

    .container-section {
        display: flex;
        flex-wrap: wrap;
        width: 100%;
        height: 100%;
        row-gap: 16px;
        column-gap: 10px;
    }

    .collapsible-content {
        overflow: auto;
        max-height: 1000px;
        opacity: 1;
        transition: max-height 0.3s ease-in-out, opacity 0.3s ease-in-out;
    }

    .collapsible-content.collapsed {
        max-height: 0;
        opacity: 0;
        margin: 0;
        padding: 0;
    }

    .container-section .item {
        display: flex;
        flex-basis: calc(33.33% - 10px);
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
        gap: 4px;
        padding: 4px 0;
    }

    .container-section .item .item-label {
        color: #646a73;
        font: 400 14px/22px 'PingFang SC';
    }

    .container-section .item .item-value {
        color: #1f2329;
        font: 500 14px/22px 'PingFang SC';
    }


    .custom-table {
        margin-bottom: 0;
        width: 100%;
        border-collapse: collapse;
        overflow: hidden;
    }

    .custom-table th {
        background-color: #f5f7fa;
        color: #646A73;
        font: 500 14px/22px 'PingFang SC';
        text-align: left;
        padding: 12px 8px;
        border-bottom: 1px solid #ebeef5;
    }

    .custom-table td {
        color: #1F2329;
        font: 400 14px/22px 'PingFang SC';
        padding: 12px 8px;
        border-bottom: 1px solid #ebeef5;
    }

    .custom-table tr:nth-child(even) {
        background-color: #f6f6f6;
    }

    .custom-table tr:last-child td {
        border-bottom: none;
    }

    .no-data {
        color: #909399;
        font: 400 14px/22px 'PingFang SC';
        text-align: center;
        padding: 20px;
        font-style: italic;
        background-color: #f9f9f9;
        border-radius: 4px;
        margin: 10px 0;
        width: 100%;
    }

    .new-accounts .section-header span {
        background: #e8f5e9;
        color: #2e7d32;
    }

    .lost-accounts .section-header span {
        background: #fbe9e7;
        color: #d84315;
    }
    </style>
