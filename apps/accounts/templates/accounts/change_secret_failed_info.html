{% load i18n %}

<h3>{% trans 'Task name' %}: {{ name }}</h3>
<h3>{% trans 'Task execution id' %}: {{ execution_id }}</h3>
<p>{% trans 'Respectful' %} {{ recipient }}</p>
<p>{% trans 'Hello! The following is the failure of changing the password of your assets or pushing the account. Please check and handle it in time.' %}</p>
<table style="width: 100%; border-collapse: collapse; max-width: 100%; text-align: left; margin-top: 20px;">
    <caption></caption>
    <thead>
    <tr style="background-color: #f2f2f2;">
        <th style="border: 1px solid #ddd; padding: 10px;">{% trans 'Asset' %}</th>
        <th style="border: 1px solid #ddd; padding: 10px;">{% trans 'Account' %}</th>
        <th style="border: 1px solid #ddd; padding: 10px;">{% trans 'Error' %}</th>
    </tr>
    </thead>
    <tbody>
    {% for asset_name, account_username, error in asset_account_errors %}
        <tr>
            <td style="border: 1px solid #ddd; padding: 10px;">{{ asset_name }}</td>
            <td style="border: 1px solid #ddd; padding: 10px;">{{ account_username }}</td>
            <td style="border: 1px solid #ddd; padding: 10px;">
                <div style="
                max-width: 90%;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                display: block;"
                     title="{{ error }}"
                >
                    {{ error }}
                </div>
            </td>
        </tr>
    {% endfor %}
    </tbody>
</table>