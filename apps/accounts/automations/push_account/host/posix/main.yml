- hosts: demo
  gather_facts: no
  tasks:
    - name: "Test privileged {{ jms_account.username }} account"
      ansible.builtin.ping:

    - name: "Check if {{ account.username }} user exists"
      getent:
        database: passwd
        key: "{{ account.username }}"
      register: user_info
      failed_when: false
      changed_when: false

    - name: "Add {{ account.username }} user"
      ansible.builtin.user:
        name: "{{ account.username }}"
        uid: "{{ params.uid | int if params.uid | length > 0 else omit }}"
        shell: "{{ params.shell if params.shell | length > 0 else omit }}"
        home: "{{ params.home if params.home | length > 0 else '/home/' + account.username }}"
        groups: "{{ params.groups if params.groups | length > 0 else omit }}"
        append: "{{ true if params.groups | length > 0 else false }}"
        expires: -1
        state: present
      when: user_info.msg is defined

    - name: "Set {{ account.username }} sudo setting"
      ansible.builtin.lineinfile:
        dest: /etc/sudoers
        state: present
        regexp: "^{{ account.username }} ALL="
        line: "{{ account.username + ' ALL=(ALL) NOPASSWD: ' + params.sudo }}"
        validate: visudo -cf %s
      when:
        - user_info.msg is defined or params.modify_sudo
        - params.sudo

    - name: "Change {{ account.username }} password"
      ansible.builtin.user:
        name: "{{ account.username }}"
        password: "{{ account.secret | password_hash('sha512') }}"
        update_password: always
      ignore_errors: true
      register: change_secret_result
      when: account.secret_type == "password"

    - name: "Get home directory for {{ account.username }}"
      ansible.builtin.shell: "getent passwd {{ account.username }} | cut -d: -f6"
      register: home_dir
      when: account.secret_type == "ssh_key"
      ignore_errors: yes

    - name: "Check if home directory exists for {{ account.username }}"
      ansible.builtin.stat:
        path: "{{ home_dir.stdout.strip() }}"
      register: home_dir_stat
      when: account.secret_type == "ssh_key"
      ignore_errors: yes

    - name: "Ensure {{ account.username }} home directory exists"
      ansible.builtin.file:
        path: "{{ home_dir.stdout.strip() }}"
        state: directory
        owner: "{{ account.username }}"
        group: "{{ account.username }}"
        mode: '0750'
      when:
        - account.secret_type == "ssh_key"
        - home_dir_stat.stat.exists == false
      ignore_errors: yes

    - name: Remove jumpserver ssh key
      ansible.builtin.lineinfile:
        dest: "{{ home_dir.stdout.strip() }}/.ssh/authorized_keys"
        regexp: "{{ ssh_params.regexp }}"
        state: absent
      when:
        - account.secret_type == "ssh_key"
        - ssh_params.strategy == "set_jms"
      ignore_errors: yes

    - name: "Change {{ account.username }} SSH key"
      ansible.builtin.authorized_key:
        user: "{{ account.username }}"
        key: "{{ account.secret }}"
        exclusive: "{{ ssh_params.exclusive }}"
      register: change_secret_result
      when: account.secret_type == "ssh_key"

    - name: Refresh connection
      ansible.builtin.meta: reset_connection

    - name: "Verify {{ account.username }} password (paramiko)"
      ssh_ping:
        login_user: "{{ account.username }}"
        login_password: "{{ account.secret }}"
        login_host: "{{ jms_asset.address }}"
        login_port: "{{ jms_asset.port }}"
        gateway_args: "{{ jms_asset.ansible_ssh_common_args | default(None) }}"
        become: "{{ account.become.ansible_become | default(False) }}"
        become_method: su
        become_user: "{{ account.become.ansible_user | default('') }}"
        become_password: "{{ account.become.ansible_password | default('') }}"
        become_private_key_path: "{{ account.become.ansible_ssh_private_key_file | default(None) }}"
        old_ssh_version: "{{ jms_asset.old_ssh_version | default(False) }}"
      when:
       - account.secret_type == "password"
       - check_conn_after_change or change_secret_result.failed | default(false)
      delegate_to: localhost

    - name: "Verify {{ account.username }} SSH KEY (paramiko)"
      ssh_ping:
        login_host: "{{ jms_asset.address }}"
        login_port: "{{ jms_asset.port }}"
        login_user: "{{ account.username }}"
        login_private_key_path: "{{ account.private_key_path  }}"
        gateway_args: "{{ jms_asset.ansible_ssh_common_args | default(None) }}"
        old_ssh_version: "{{ jms_asset.old_ssh_version | default(False) }}"
      when:
       - account.secret_type == "ssh_key"
       - check_conn_after_change or change_secret_result.failed | default(false)
      delegate_to: localhost

