- hosts: mysql
  gather_facts: no
  vars:
    ansible_python_interpreter: "{{ local_python_interpreter }}"
    db_name: "{{ jms_asset.spec_info.db_name }}"
    check_ssl: "{{ jms_asset.spec_info.use_ssl and not jms_asset.spec_info.allow_invalid_cert }}"
    ca_cert: "{{ jms_asset.secret_info.ca_cert | default('') }}"
    ssl_cert: "{{ jms_asset.secret_info.client_cert | default('') }}"
    ssl_key: "{{ jms_asset.secret_info.client_key | default('') }}"

  tasks:
    - name: Test MySQL connection
      community.mysql.mysql_info:
        login_user: "{{ jms_account.username }}"
        login_password: "{{ jms_account.secret }}"
        login_host: "{{ jms_asset.address }}"
        login_port: "{{ jms_asset.port }}"
        check_hostname: "{{ check_ssl if check_ssl else omit }}"
        ca_cert: "{{ ca_cert if check_ssl and ca_cert | length > 0 else omit }}"
        client_cert: "{{ ssl_cert if check_ssl and ssl_cert | length > 0 else omit }}"
        client_key: "{{ ssl_key if check_ssl and ssl_key | length > 0 else omit }}"
        filter: version
      register: db_info

    - name: MySQL version
      debug:
        var: db_info.version.full

    - name: Change MySQL password
      community.mysql.mysql_user:
        login_user: "{{ jms_account.username }}"
        login_password: "{{ jms_account.secret }}"
        login_host: "{{ jms_asset.address }}"
        login_port: "{{ jms_asset.port }}"
        check_hostname: "{{ check_ssl if check_ssl else omit }}"
        ca_cert: "{{ ca_cert if check_ssl and ca_cert | length > 0 else omit }}"
        client_cert: "{{ ssl_cert if check_ssl and ssl_cert | length > 0 else omit }}"
        client_key: "{{ ssl_key if check_ssl and ssl_key | length > 0 else omit }}"
        name: "{{ account.username }}"
        password: "{{ account.secret }}"
        host: "%"
        priv: "{{ account.username + '.*:USAGE' if db_name == '' else db_name + '.*:ALL' }}"
      ignore_errors: true
      when: db_info is succeeded

    - name: Verify password
      community.mysql.mysql_info:
        login_user: "{{ account.username }}"
        login_password: "{{ account.secret }}"
        login_host: "{{ jms_asset.address }}"
        login_port: "{{ jms_asset.port }}"
        check_hostname: "{{ check_ssl if check_ssl else omit }}"
        ca_cert: "{{ ca_cert if check_ssl and ca_cert | length > 0 else omit }}"
        client_cert: "{{ ssl_cert if check_ssl and ssl_cert | length > 0 else omit }}"
        client_key: "{{ ssl_key if check_ssl and ssl_key | length > 0 else omit }}"
        filter: version
      when: check_conn_after_change
