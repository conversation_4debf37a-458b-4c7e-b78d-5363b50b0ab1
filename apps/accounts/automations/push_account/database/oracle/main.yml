- hosts: oracle
  gather_facts: no
  vars:
    ansible_python_interpreter: "{{ local_python_interpreter }}"

  tasks:
    - name: Test Oracle connection
      oracle_ping:
        login_user: "{{ jms_account.username }}"
        login_password: "{{ jms_account.secret }}"
        login_host: "{{ jms_asset.address }}"
        login_port: "{{ jms_asset.port }}"
        login_database: "{{ jms_asset.spec_info.db_name }}"
        mode: "{{ jms_account.mode }}"
      register: db_info

    - name: Display Oracle version
      debug:
        var: db_info.server_version
      when: db_info is succeeded

    - name: Change Oracle password
      oracle_user:
        login_user: "{{ jms_account.username }}"
        login_password: "{{ jms_account.secret }}"
        login_host: "{{ jms_asset.address }}"
        login_port: "{{ jms_asset.port }}"
        login_database: "{{ jms_asset.spec_info.db_name }}"
        mode: "{{ jms_account.mode }}"
        name: "{{ account.username }}"
        password: "{{ account.secret }}"
      ignore_errors: true
      when: db_info is succeeded

    - name: Verify password
      oracle_ping:
        login_user: "{{ account.username }}"
        login_password: "{{ account.secret }}"
        login_host: "{{ jms_asset.address }}"
        login_port: "{{ jms_asset.port }}"
        login_database: "{{ jms_asset.spec_info.db_name }}"
        mode: "{{ account.mode }}"
      when: check_conn_after_change
