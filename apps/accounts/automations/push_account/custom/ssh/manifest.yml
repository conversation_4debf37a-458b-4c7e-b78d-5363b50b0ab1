id: push_account_by_ssh
name: "{{ 'SSH account push' | trans }}"
category:
  - device
  - host
type:
  - all
method: push_account
protocol: ssh
priority: 50
params:
  - name: commands
    type: text
    label: "{{ 'Params commands label' | trans }}"
    default: ''
    help_text: "{{ 'Params commands help text' | trans }}"
  - name: recv_timeout
    type: int
    label: "{{ 'Params recv_timeout label' | trans }}"
    default: 30
    help_text: "{{ 'Params recv_timeout help text' | trans }}"
  - name: delay_time
    type: int
    label: "{{ 'Params delay_time label' | trans }}"
    default: 2
    help_text: "{{ 'Params delay_time help text' | trans }}"
  - name: prompt
    type: str
    label: "{{ 'Params prompt label' | trans }}"
    default: '.*'
    help_text: "{{ 'Params prompt help text' | trans }}"
  - name: answers
    type: text
    label: "{{ 'Params answer label' | trans }}"
    default: '.*'
    help_text: "{{ 'Params answer help text' | trans }}"

i18n:
  SSH account push:
    zh: '使用 SSH 命令行自定义推送'
    ja: 'SSHコマンドラインを使用してプッシュをカスタマイズする'
    en: 'Custom push using SSH command line'

  Params commands help text:
    zh: |
      请将命令中的指定位置改成特殊符号 <br />
      1. 推送账号 -> {username} <br />
      2. 推送密码 -> {password} <br />
      3. 登录用户密码 -> {login_password} <br />
      <strong>多条命令使用换行分割，</strong>执行任务时系统会根据特殊符号替换真实数据。<br />
      比如针对 Cisco 主机进行推送，一般需要配置五条命令：<br />
      enable <br />
      {login_password} <br />
      configure terminal <br />
      username {username} privilege 0 password {password} <br />
      end <br />
    ja: |
      コマンド内の指定された位置を特殊記号に変更してください。<br />
      新しいパスワード（アカウント押す） -> {username} <br />
      新しいパスワード（パスワード押す） -> {password} <br />
      ログインユーザーパスワード -> {login_password} <br />
      <strong>複数のコマンドは改行で区切り、</strong>タスクを実行するときにシステムは特殊記号を使用して実際のデータを置き換えます。<br />
      例えば、Cisco機器のパスワードを変更する場合、一般的には5つのコマンドを設定する必要があります：<br />
      enable <br />
      {login_password} <br />
      configure terminal <br />
      username {username} privilege 0 password {password} <br />
      end <br />
    en: |
      Please change the specified positions in the command to special symbols. <br /> 
      Change password account -> {username} <br /> 
      Change password -> {password} <br /> 
      Login user password -> {login_password} <br /> 
      <strong>Multiple commands are separated by new lines,</strong> and when executing tasks, <br /> 
      the system will replace the special symbols with real data. <br /> 
      For example, to push the password for a Cisco device, you generally need to configure five commands: <br />
      enable <br />
      {login_password} <br />
      configure terminal <br />
      username {username} privilege 0 password {password} <br />
      end <br />

  Params commands label:
    zh: '自定义命令'
    ja: 'カスタムコマンド'
    en: 'Custom command'

  Params recv_timeout label:
    zh: '超时时间'
    ja: 'タイムアウト'
    en: 'Timeout'

  Params recv_timeout help text:
    zh: '等待命令结果返回的超时时间（秒）'
    ja: 'コマンドの結果を待つタイムアウト時間（秒）'
    en: 'The timeout for waiting for the command result to return (Seconds)'

  Params delay_time label:
    zh: '延迟发送时间'
    ja: '遅延送信時間'
    en: 'Delayed send time'

  Params delay_time help text:
    zh: '每条命令延迟发送的时间间隔（秒）'
    ja: '各コマンド送信の遅延間隔（秒）'
    en: 'Time interval for each command delay in sending (Seconds)'

  Params prompt label:
    zh: '提示符'
    ja: 'ヒント'
    en: 'Prompt'

  Params prompt help text:
    zh: '终端连接后显示的提示符信息（正则表达式）'
    ja: 'ターミナル接続後に表示されるプロンプト情報（正規表現）'
    en: 'Prompt information displayed after terminal connection (Regular expression)'

  Params answer label:
    zh: '命令结果'
    ja: 'コマンド結果'
    en: 'Command result'

  Params answer help text:
    zh: |
      根据结果匹配度决定是否执行下一条命令，输入框的内容和上方 “自定义命令” 内容按行一一对应（正则表达式）
    ja: |
      結果の一致度に基づいて次のコマンドを実行するかどうかを決定します。
      入力欄の内容は、上の「カスタムコマンド」の内容と行ごとに対応しています（せいきひょうげん）
    en: |
      Decide whether to execute the next command based on the result match. 
      The input content corresponds line by line with the content 
      of the `Custom command` above. (Regular expression)
