- hosts: sqlserver
  gather_facts: no
  vars:
    ansible_python_interpreter: "{{ local_python_interpreter }}"

  tasks:
    - name: Verify account
      community.general.mssql_script:
        login_user: "{{ account.username }}"
        login_password: "{{ account.secret }}"
        login_host: "{{ jms_asset.address }}"
        login_port: "{{ jms_asset.port }}"
        name: '{{ jms_asset.spec_info.db_name }}'
        script: |
          SELECT @@version
