- hosts: postgresql
  gather_facts: no
  vars:
    ansible_python_interpreter: "{{ local_python_interpreter }}"
    check_ssl: "{{ jms_asset.spec_info.use_ssl }}"
    ca_cert: "{{ jms_asset.secret_info.ca_cert | default('') }}"
    ssl_cert: "{{ jms_asset.secret_info.client_cert | default('') }}"
    ssl_key: "{{ jms_asset.secret_info.client_key | default('') }}"

  tasks:
    - name: Get info
      community.postgresql.postgresql_info:
        login_user: "{{ jms_account.username }}"
        login_password: "{{ jms_account.secret }}"
        login_host: "{{ jms_asset.address }}"
        login_port: "{{ jms_asset.port }}"
        login_db: "{{ jms_asset.spec_info.db_name }}"
        ca_cert: "{{ ca_cert if check_ssl and ca_cert | length > 0 else omit }}"
        ssl_cert: "{{ ssl_cert if check_ssl and ssl_cert | length > 0 else omit }}"
        ssl_key: "{{ ssl_key if check_ssl and ssl_key | length > 0 else omit }}"
        ssl_mode: "{{ jms_asset.spec_info.pg_ssl_mode }}"
        filter: "roles"
      register: db_info

    - name: Define info by set_fact
      set_fact:
        info: "{{ db_info.roles }}"

    - debug:
        var: info
