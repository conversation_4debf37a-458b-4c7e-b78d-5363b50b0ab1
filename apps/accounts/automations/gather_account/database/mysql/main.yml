- hosts: mysql
  gather_facts: no
  vars:
    ansible_python_interpreter: "{{ local_python_interpreter }}"
    check_ssl: "{{ jms_asset.spec_info.use_ssl and not jms_asset.spec_info.allow_invalid_cert }}"
    ca_cert: "{{ jms_asset.secret_info.ca_cert | default('') }}"
    ssl_cert: "{{ jms_asset.secret_info.client_cert | default('') }}"
    ssl_key: "{{ jms_asset.secret_info.client_key | default('') }}"

  tasks:
    - name: Get info
      community.mysql.mysql_info:
        login_user: "{{ jms_account.username }}"
        login_password: "{{ jms_account.secret }}"
        login_host: "{{ jms_asset.address }}"
        login_port: "{{ jms_asset.port }}"
        check_hostname: "{{ check_ssl if check_ssl else omit }}"
        ca_cert: "{{ ca_cert if check_ssl and ca_cert | length > 0 else omit }}"
        client_cert: "{{ ssl_cert if check_ssl and ssl_cert | length > 0 else omit }}"
        client_key: "{{ ssl_key if check_ssl and ssl_key | length > 0 else omit }}"
        filter: users
      register: db_info

    - name: Define info by set_fact
      set_fact:
        info: "{{ db_info.users }}"

    - debug:
        var: info
