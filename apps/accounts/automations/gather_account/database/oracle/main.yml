- hosts: oralce
  gather_facts: no
  vars:
    ansible_python_interpreter: "{{ local_python_interpreter }}"

  tasks:
    - name: Get info
      oracle_info:
        login_user: "{{ jms_account.username }}"
        login_password: "{{ jms_account.secret }}"
        login_host: "{{ jms_asset.address }}"
        login_port: "{{ jms_asset.port }}"
        login_database: "{{ jms_asset.spec_info.db_name }}"
        mode: "{{ jms_account.mode }}"
        filter: users
      register: db_info

    - name: Define info by set_fact
      set_fact:
        info: "{{ db_info.users }}"

    - debug:
        var: info
