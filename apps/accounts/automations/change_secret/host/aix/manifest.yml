id: change_secret_aix
name: "{{ 'AIX account change secret' | trans }}"
category: host
type:
  - AIX
method: change_secret
params:
  - name: modify_sudo
    type: bool
    label: "{{ 'Modify sudo label' | trans }}"
    default: False
    help_text: "{{ 'Modify params sudo help text' | trans }}"

  - name: sudo
    type: str
    label: 'Sudo'
    default: '/bin/whoami'
    help_text: "{{ 'Params sudo help text' | trans }}"

  - name: shell
    type: str
    label: 'Shell'
    default: '/bin/bash'

  - name: home
    type: str
    label: "{{ 'Params home label' | trans }}"
    default: ''
    help_text: "{{ 'Params home help text' | trans }}"

  - name: groups
    type: str
    label: "{{ 'Params groups label' | trans }}"
    default: ''
    help_text: "{{ 'Params groups help text' | trans }}"

  - name: uid
    type: str
    label: "{{ 'Params uid label' | trans }}"
    default: ''
    help_text: "{{ 'Params uid help text' | trans }}"

i18n:
  AIX account change secret:
    zh: '使用 Ansible 模块 user 执行账号改密 (DES)'
    ja: 'Ansible user モジュールを使用してアカウントのパスワード変更 (DES)'
    en: 'Using Ansible module user to change account secret (DES)'

  Modify params sudo help text:
    zh: '如果用户存在，可以修改sudo权限'
    ja: 'ユーザーが存在する場合、sudo権限を変更できます'
    en: 'If the user exists, sudo permissions can be modified'

  Params sudo help text:
    zh: '使用逗号分隔多个命令，如: /bin/whoami,/sbin/ifconfig'
    ja: 'コンマで区切って複数のコマンドを入力してください。例: /bin/whoami,/sbin/ifconfig'
    en: 'Use commas to separate multiple commands, such as: /bin/whoami,/sbin/ifconfig'

  Params home help text:
    zh: '默认家目录 /home/<USER>'
    ja: 'デフォルトのホームディレクトリ /home/<USER>'
    en: 'Default home directory /home/<USER>'

  Params groups help text:
    zh: '请输入用户组，多个用户组使用逗号分隔（需填写已存在的用户组）'
    ja: 'グループを入力してください。複数のグループはコンマで区切ってください（既存のグループを入力してください）'
    en: 'Please enter the group. Multiple groups are separated by commas (please enter the existing group)'

  Params uid help text:
    zh: '请输入用户ID'
    ja: 'ユーザーIDを入力してください'
    en: 'Please enter the user ID'

  Modify sudo label:
    zh: '修改 sudo 权限'
    ja: 'sudo 権限を変更'
    en: 'Modify sudo'

  Params home label:
    zh: '家目录'
    ja: 'ホームディレクトリ'
    en: 'Home'

  Params groups label:
    zh: '用户组'
    ja: 'グループ'
    en: 'Groups'

  Params uid label:
    zh: '用户ID'
    ja: 'ユーザーID'
    en: 'User ID'
