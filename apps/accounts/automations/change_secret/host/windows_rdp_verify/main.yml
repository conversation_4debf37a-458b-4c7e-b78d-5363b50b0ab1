- hosts: demo
  gather_facts: no
  tasks:
    - name: Test privileged account
      ansible.windows.win_ping:

    - name: Change password
      ansible.windows.win_user:
        fullname: "{{ account.username}}"
        name: "{{ account.username }}"
        password: "{{ account.secret }}"
        password_never_expires: yes
        groups: "{{ params.groups }}"
        groups_action: add
        update_password: always
      ignore_errors: true
      when: account.secret_type == "password"

    - name: Refresh connection
      ansible.builtin.meta: reset_connection

    - name: Verify password (pyfreerdp)
      rdp_ping:
        login_host: "{{ jms_asset.origin_address }}"
        login_port: "{{ jms_asset.protocols | selectattr('name', 'equalto', 'rdp') | map(attribute='port') | first }}"
        login_user: "{{ account.username }}"
        login_password: "{{ account.secret }}"
        login_secret_type: "{{ account.secret_type }}"
        gateway_args: "{{ jms_gateway | default({}) }}"
      when: account.secret_type == "password" and check_conn_after_change
      delegate_to: localhost
