# Generated by Django 4.1.13 on 2025-03-11 09:39

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ('accounts', '0005_accountrisk_backupaccountautomation_and_more'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='accountrisk',
            name='username',
            field=models.Char<PERSON>ield(max_length=128, verbose_name='Username'),
        ),
        migrations.AlterField(
            model_name='gatheredaccount',
            name='address_last_login',
            field=models.Char<PERSON>ield(default='', max_length=45, null=True, verbose_name='Address login'),
        ),
        migrations.Alter<PERSON>ield(
            model_name='gatheredaccount',
            name='username',
            field=models.Char<PERSON>ield(blank=True, db_index=True, max_length=128, verbose_name='Username'),
        ),
    ]
