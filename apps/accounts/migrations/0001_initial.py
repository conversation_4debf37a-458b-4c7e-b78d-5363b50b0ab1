# Generated by Django 4.1.13 on 2024-05-09 03:16

import uuid

import simple_history.models
from django.db import migrations, models

import common.db.encoder
import common.db.fields


class Migration(migrations.Migration):
    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Account',
            fields=[
                ('created_by', models.CharField(blank=True, max_length=128, null=True, verbose_name='Created by')),
                ('updated_by', models.CharField(blank=True, max_length=128, null=True, verbose_name='Updated by')),
                ('date_created', models.DateTimeField(auto_now_add=True, null=True, verbose_name='Date created')),
                ('date_updated', models.DateTimeField(auto_now=True, verbose_name='Date updated')),
                ('comment', models.TextField(blank=True, default='', verbose_name='Comment')),
                ('id', models.UUIDField(default=uuid.uuid4, primary_key=True, serialize=False)),
                ('org_id',
                 models.Char<PERSON>ield(blank=True, db_index=True, default='', max_length=36, verbose_name='Organization')),
                ('connectivity',
                 models.Char<PERSON>ield(choices=[('-', 'Unknown'), ('ok', 'OK'), ('err', 'Error')], default='-',
                                  max_length=16, verbose_name='Connectivity')),
                ('date_verified', models.DateTimeField(null=True, verbose_name='Date verified')),
                ('_secret', common.db.fields.EncryptTextField(blank=True, null=True, verbose_name='Secret')),
                ('name', models.CharField(max_length=128, verbose_name='Name')),
                ('username', models.CharField(blank=True, db_index=True, max_length=128, verbose_name='Username')),
                ('secret_type', models.CharField(
                    choices=[('password', 'Password'), ('ssh_key', 'SSH key'), ('access_key', 'Access key'),
                             ('token', 'Token'), ('api_key', 'API key')], default='password', max_length=16,
                    verbose_name='Secret type')),
                ('privileged', models.BooleanField(default=False, verbose_name='Privileged')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is active')),
                ('version', models.IntegerField(default=0, verbose_name='Version')),
                ('source', models.CharField(default='local', max_length=30, verbose_name='Source')),
                ('source_id', models.CharField(blank=True, max_length=128, null=True, verbose_name='Source ID')),
            ],
            options={
                'verbose_name': 'Account',
                'permissions': [('view_accountsecret', 'Can view asset account secret'),
                                ('view_historyaccount', 'Can view asset history account'),
                                ('view_historyaccountsecret', 'Can view asset history account secret'),
                                ('verify_account', 'Can verify account'), ('push_account', 'Can push account'),
                                ('remove_account', 'Can remove account')],
            },
        ),
        migrations.CreateModel(
            name='AccountBackupAutomation',
            fields=[
                ('created_by', models.CharField(blank=True, max_length=128, null=True, verbose_name='Created by')),
                ('updated_by', models.CharField(blank=True, max_length=128, null=True, verbose_name='Updated by')),
                ('date_created', models.DateTimeField(auto_now_add=True, null=True, verbose_name='Date created')),
                ('date_updated', models.DateTimeField(auto_now=True, verbose_name='Date updated')),
                ('comment', models.TextField(blank=True, default='', verbose_name='Comment')),
                ('id', models.UUIDField(default=uuid.uuid4, primary_key=True, serialize=False)),
                ('org_id',
                 models.CharField(blank=True, db_index=True, default='', max_length=36, verbose_name='Organization')),
                ('name', models.CharField(max_length=128, verbose_name='Name')),
                ('is_periodic', models.BooleanField(default=False, verbose_name='Periodic run')),
                ('interval', models.IntegerField(blank=True, default=24, null=True, verbose_name='Interval')),
                ('crontab', models.CharField(blank=True, max_length=128, null=True, verbose_name='Crontab')),
                ('types', models.JSONField(default=list)),
                ('backup_type',
                 models.CharField(choices=[('email', 'Email'), ('object_storage', 'SFTP')], default='email',
                                  max_length=128, verbose_name='Backup type')),
                ('is_password_divided_by_email', models.BooleanField(default=True, verbose_name='Password divided')),
                ('is_password_divided_by_obj_storage',
                 models.BooleanField(default=True, verbose_name='Password divided')),
                ('zip_encrypt_password', common.db.fields.EncryptCharField(blank=True, max_length=4096, null=True,
                                                                           verbose_name='Zip encrypt password')),
            ],
            options={
                'verbose_name': 'Account backup plan',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='AccountBackupExecution',
            fields=[
                ('org_id',
                 models.CharField(blank=True, db_index=True, default='', max_length=36, verbose_name='Organization')),
                ('id', models.UUIDField(default=uuid.uuid4, primary_key=True, serialize=False)),
                ('date_start', models.DateTimeField(auto_now_add=True, verbose_name='Date start')),
                ('timedelta', models.FloatField(default=0.0, null=True, verbose_name='Time')),
                ('snapshot',
                 models.JSONField(blank=True, default=dict, encoder=common.db.encoder.ModelJSONFieldEncoder, null=True,
                                  verbose_name='Account backup snapshot')),
                ('trigger', models.CharField(choices=[('manual', 'Manual'), ('timing', 'Timing')],
                                             default='manual', max_length=128, verbose_name='Trigger mode')),
                ('reason', models.CharField(blank=True, max_length=1024, null=True, verbose_name='Reason')),
                ('is_success', models.BooleanField(default=False, verbose_name='Is success')),
            ],
            options={
                'verbose_name': 'Account backup execution',
                'ordering': ('-date_start',),
            },
        ),
        migrations.CreateModel(
            name='AccountTemplate',
            fields=[
                ('created_by', models.CharField(blank=True, max_length=128, null=True, verbose_name='Created by')),
                ('updated_by', models.CharField(blank=True, max_length=128, null=True, verbose_name='Updated by')),
                ('date_created', models.DateTimeField(auto_now_add=True, null=True, verbose_name='Date created')),
                ('date_updated', models.DateTimeField(auto_now=True, verbose_name='Date updated')),
                ('comment', models.TextField(blank=True, default='', verbose_name='Comment')),
                ('id', models.UUIDField(default=uuid.uuid4, primary_key=True, serialize=False)),
                ('org_id',
                 models.CharField(blank=True, db_index=True, default='', max_length=36, verbose_name='Organization')),
                ('_secret', common.db.fields.EncryptTextField(blank=True, null=True, verbose_name='Secret')),
                ('secret_strategy',
                 models.CharField(choices=[('specific', 'Specific secret'), ('random', 'Random generate')],
                                  default='specific', max_length=16, verbose_name='Secret strategy')),
                ('password_rules', models.JSONField(default=dict, verbose_name='Password rules')),
                ('name', models.CharField(max_length=128, verbose_name='Name')),
                ('username', models.CharField(blank=True, db_index=True, max_length=128, verbose_name='Username')),
                ('secret_type', models.CharField(
                    choices=[('password', 'Password'), ('ssh_key', 'SSH key'), ('access_key', 'Access key'),
                             ('token', 'Token'), ('api_key', 'API key')], default='password', max_length=16,
                    verbose_name='Secret type')),
                ('privileged', models.BooleanField(default=False, verbose_name='Privileged')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is active')),
                ('auto_push', models.BooleanField(default=False, verbose_name='Auto push')),
                ('push_params', models.JSONField(default=dict, verbose_name='Push params')),
            ],
            options={
                'verbose_name': 'Account template',
                'permissions': [('view_accounttemplatesecret', 'Can view asset account template secret')],
            },
        ),
        migrations.CreateModel(
            name='ChangeSecretRecord',
            fields=[
                ('created_by', models.CharField(blank=True, max_length=128, null=True, verbose_name='Created by')),
                ('updated_by', models.CharField(blank=True, max_length=128, null=True, verbose_name='Updated by')),
                ('date_created', models.DateTimeField(auto_now_add=True, null=True, verbose_name='Date created')),
                ('date_updated', models.DateTimeField(auto_now=True, verbose_name='Date updated')),
                ('comment', models.TextField(blank=True, default='', verbose_name='Comment')),
                ('id', models.UUIDField(default=uuid.uuid4, primary_key=True, serialize=False)),
                ('old_secret', common.db.fields.EncryptTextField(blank=True, null=True, verbose_name='Old secret')),
                ('new_secret', common.db.fields.EncryptTextField(blank=True, null=True, verbose_name='New secret')),
                ('date_started', models.DateTimeField(blank=True, null=True, verbose_name='Date started')),
                ('date_finished', models.DateTimeField(blank=True, null=True, verbose_name='Date finished')),
                ('status', models.CharField(default='pending', max_length=16, verbose_name='Status')),
                ('error', models.TextField(blank=True, null=True, verbose_name='Error')),
            ],
            options={
                'verbose_name': 'Change secret record',
                'ordering': ('-date_created',),
            },
        ),
        migrations.CreateModel(
            name='GatheredAccount',
            fields=[
                ('created_by', models.CharField(blank=True, max_length=128, null=True, verbose_name='Created by')),
                ('updated_by', models.CharField(blank=True, max_length=128, null=True, verbose_name='Updated by')),
                ('date_created', models.DateTimeField(auto_now_add=True, null=True, verbose_name='Date created')),
                ('date_updated', models.DateTimeField(auto_now=True, verbose_name='Date updated')),
                ('comment', models.TextField(blank=True, default='', verbose_name='Comment')),
                ('id', models.UUIDField(default=uuid.uuid4, primary_key=True, serialize=False)),
                ('org_id',
                 models.CharField(blank=True, db_index=True, default='', max_length=36, verbose_name='Organization')),
                ('present', models.BooleanField(default=True, verbose_name='Remote present')),
                ('date_last_login', models.DateTimeField(null=True, verbose_name='Date login')),
                ('username', models.CharField(blank=True, db_index=True, max_length=32, verbose_name='Username')),
                ('address_last_login', models.CharField(default='', max_length=39, verbose_name='Address login')),
            ],
            options={
                'verbose_name': 'Gather asset accounts',
                'ordering': ['asset'],
            },
        ),
        migrations.CreateModel(
            name='HistoricalAccount',
            fields=[
                ('id', models.UUIDField(db_index=True, default=uuid.uuid4)),
                ('_secret', common.db.fields.EncryptTextField(blank=True, null=True, verbose_name='Secret')),
                ('secret_type', models.CharField(
                    choices=[('password', 'Password'), ('ssh_key', 'SSH key'), ('access_key', 'Access key'),
                             ('token', 'Token'), ('api_key', 'API key')], default='password', max_length=16,
                    verbose_name='Secret type')),
                ('version', models.IntegerField(default=0, verbose_name='Version')),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type',
                 models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
            ],
            options={
                'verbose_name': 'historical Account',
                'verbose_name_plural': 'historical Accounts',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='VirtualAccount',
            fields=[
                ('created_by', models.CharField(blank=True, max_length=128, null=True, verbose_name='Created by')),
                ('updated_by', models.CharField(blank=True, max_length=128, null=True, verbose_name='Updated by')),
                ('date_created', models.DateTimeField(auto_now_add=True, null=True, verbose_name='Date created')),
                ('date_updated', models.DateTimeField(auto_now=True, verbose_name='Date updated')),
                ('id', models.UUIDField(default=uuid.uuid4, primary_key=True, serialize=False)),
                ('org_id',
                 models.CharField(blank=True, db_index=True, default='', max_length=36, verbose_name='Organization')),
                ('alias', models.CharField(
                    choices=[('@INPUT', 'Manual input'), ('@USER', 'Dynamic user'), ('@ANON', 'Anonymous account'),
                             ('@SPEC', 'Specified account')], max_length=128, verbose_name='Alias')),
                ('secret_from_login', models.BooleanField(default=None, null=True, verbose_name='Secret from login')),
            ],
            options={'verbose_name': 'Virtual account'},
        ),
    ]
