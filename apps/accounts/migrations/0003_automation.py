# Generated by Django 4.1.13 on 2024-05-09 03:16

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('assets', '0001_initial'),
        ('terminal', '0001_initial'),
        ('accounts', '0002_auto_20220616_0021'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='historicalaccount',
            name='history_user',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='gatheredaccount',
            name='asset',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='assets.asset', verbose_name='Asset'),
        ),
        migrations.AddField(
            model_name='changesecretrecord',
            name='account',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='accounts.account'),
        ),
        migrations.AddField(
            model_name='changesecretrecord',
            name='asset',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='assets.asset'),
        ),
        migrations.AddField(
            model_name='changesecretrecord',
            name='execution',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounts.automationexecution'),
        ),
        migrations.AddField(
            model_name='accounttemplate',
            name='platforms',
            field=models.ManyToManyField(blank=True, related_name='account_templates', to='assets.platform', verbose_name='Platforms'),
        ),
        migrations.AddField(
            model_name='accounttemplate',
            name='su_from',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='su_to', to='accounts.accounttemplate', verbose_name='Su from'),
        ),
        migrations.AddField(
            model_name='accountbackupexecution',
            name='plan',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='execution', to='accounts.accountbackupautomation', verbose_name='Account backup plan'),
        ),
        migrations.AddField(
            model_name='accountbackupautomation',
            name='obj_recipients_part_one',
            field=models.ManyToManyField(blank=True, related_name='obj_recipient_part_one_plans', to='terminal.replaystorage', verbose_name='Object storage recipient part one'),
        ),
        migrations.AddField(
            model_name='accountbackupautomation',
            name='obj_recipients_part_two',
            field=models.ManyToManyField(blank=True, related_name='obj_recipient_part_two_plans', to='terminal.replaystorage', verbose_name='Object storage recipient part two'),
        ),
        migrations.AddField(
            model_name='accountbackupautomation',
            name='recipients_part_one',
            field=models.ManyToManyField(blank=True, related_name='recipient_part_one_plans', to=settings.AUTH_USER_MODEL, verbose_name='Recipient part one'),
        ),
        migrations.AddField(
            model_name='accountbackupautomation',
            name='recipients_part_two',
            field=models.ManyToManyField(blank=True, related_name='recipient_part_two_plans', to=settings.AUTH_USER_MODEL, verbose_name='Recipient part two'),
        ),
        migrations.AddField(
            model_name='account',
            name='asset',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='accounts', to='assets.asset', verbose_name='Asset'),
        ),
        migrations.AddField(
            model_name='account',
            name='su_from',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='su_to', to='accounts.account', verbose_name='Su from'),
        ),
        migrations.AlterUniqueTogether(
            name='gatheredaccount',
            unique_together={('username', 'asset')},
        ),
        migrations.AddField(
            model_name='gatheraccountsautomation',
            name='recipients',
            field=models.ManyToManyField(blank=True, to=settings.AUTH_USER_MODEL, verbose_name='Recipient'),
        ),
        migrations.AddField(
            model_name='changesecretautomation',
            name='recipients',
            field=models.ManyToManyField(blank=True, to=settings.AUTH_USER_MODEL, verbose_name='Recipient'),
        ),
        migrations.AlterUniqueTogether(
            name='accounttemplate',
            unique_together={('name', 'org_id')},
        ),
        migrations.AlterUniqueTogether(
            name='accountbackupautomation',
            unique_together={('name', 'org_id')},
        ),
        migrations.AlterUniqueTogether(
            name='account',
            unique_together={('name', 'asset'), ('username', 'asset', 'secret_type')},
        ),
    ]
