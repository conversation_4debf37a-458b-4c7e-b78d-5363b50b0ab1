# Generated by Django 4.1.13 on 2024-05-09 03:16

import django.db.models.deletion
from django.db import migrations, models

import common.db.fields


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('assets', '0001_initial'),
        ('accounts', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='AccountBaseAutomation',
            fields=[
            ],
            options={
                'verbose_name': 'Account automation task',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('assets.baseautomation',),
        ),
        migrations.CreateModel(
            name='AutomationExecution',
            fields=[
            ],
            options={
                'verbose_name': 'Automation execution',
                'verbose_name_plural': 'Automation executions',
                'permissions': [('view_changesecretexecution', 'Can view change secret execution'), ('add_changesecretexecution', 'Can add change secret execution'), ('view_gatheraccountsexecution', 'Can view gather accounts execution'), ('add_gatheraccountsexecution', 'Can add gather accounts execution'), ('view_pushaccountexecution', 'Can view push account execution'), ('add_pushaccountexecution', 'Can add push account execution')],
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('assets.automationexecution',),
        ),
        migrations.CreateModel(
            name='ChangeSecretAutomation',
            fields=[
                ('baseautomation_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='assets.baseautomation')),
                ('secret_type', models.CharField(choices=[('password', 'Password'), ('ssh_key', 'SSH key'), ('access_key', 'Access key'), ('token', 'Token'), ('api_key', 'API key')], default='password', max_length=16, verbose_name='Secret type')),
                ('secret', common.db.fields.EncryptTextField(blank=True, null=True, verbose_name='Secret')),
                ('secret_strategy', models.CharField(choices=[('specific', 'Specific secret'), ('random', 'Random generate')], default='specific', max_length=16, verbose_name='Secret strategy')),
                ('password_rules', models.JSONField(default=dict, verbose_name='Password rules')),
                ('ssh_key_change_strategy', models.CharField(
                    choices=[
                        ("set_jms", "Replace (Replace only keys pushed by JumpServer) "),
                        ("set", "Empty and append SSH KEY"),
                    ],
                    default="set_jms",
                    max_length=16,
                    verbose_name="SSH key change strategy",
                )),
            ],
            options={
                'verbose_name': 'Change secret automation',
            },
            bases=('accounts.accountbaseautomation', models.Model),
        ),
        migrations.CreateModel(
            name='GatherAccountsAutomation',
            fields=[
                ('baseautomation_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='assets.baseautomation')),
                ('is_sync_account', models.BooleanField(blank=True, default=False, verbose_name='Is sync account')),
            ],
            options={
                'verbose_name': 'Gather account automation',
            },
            bases=('accounts.accountbaseautomation',),
        ),
        migrations.CreateModel(
            name='PushAccountAutomation',
            fields=[
                ('baseautomation_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='assets.baseautomation')),
                ('secret_type', models.CharField(choices=[('password', 'Password'), ('ssh_key', 'SSH key'), ('access_key', 'Access key'), ('token', 'Token'), ('api_key', 'API key')], default='password', max_length=16, verbose_name='Secret type')),
                ('secret', common.db.fields.EncryptTextField(blank=True, null=True, verbose_name='Secret')),
                ('secret_strategy', models.CharField(choices=[('specific', 'Specific secret'), ('random', 'Random generate')], default='specific', max_length=16, verbose_name='Secret strategy')),
                ('password_rules', models.JSONField(default=dict, verbose_name='Password rules')),
                ('ssh_key_change_strategy', models.CharField(
                    choices=[
                        ("set_jms", "Replace (Replace only keys pushed by JumpServer) "),
                        ("set", "Empty and append SSH KEY"),
                    ],
                    default="set_jms",
                    max_length=16,
                    verbose_name="SSH key change strategy",
                )),
                ('triggers', models.JSONField(default=list, max_length=16, verbose_name='Triggers')),
                ('username', models.CharField(max_length=128, verbose_name='Username')),
                ('action', models.CharField(max_length=16, verbose_name='Action')),
            ],
            options={
                'verbose_name': 'Push asset account',
            },
            bases=('accounts.accountbaseautomation', models.Model),
        ),
        migrations.CreateModel(
            name='VerifyAccountAutomation',
            fields=[
                ('baseautomation_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='assets.baseautomation')),
            ],
            options={
                'verbose_name': 'Verify asset account',
            },
            bases=('accounts.accountbaseautomation',),
        ),
        migrations.AlterUniqueTogether(
            name='virtualaccount',
            unique_together={('alias', 'org_id')},
        ),
    ]
