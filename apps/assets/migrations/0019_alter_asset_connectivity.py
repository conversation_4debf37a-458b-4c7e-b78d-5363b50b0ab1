# Generated by Django 4.1.13 on 2025-05-06 10:23

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ('assets', '0018_rename_domain_zone'),
    ]

    operations = [
        migrations.AlterField(
            model_name='asset',
            name='connectivity',
            field=models.CharField(
                choices=[
                    ('-', 'Unknown'),
                    ('na', 'N/A'),
                    ('ok', 'OK'),
                    ('err', 'Error'),
                    ('rdp_err', 'RDP error'),
                    ('auth_err', 'Authentication error'),
                    ('password_err', 'Invalid password error'),
                    ('openssh_key_err', 'OpenSSH key error'),
                    ('ntlm_err', 'NTLM credentials rejected error'),
                    ('create_temp_err', 'Create temporary error')
                ], default='-', max_length=16, verbose_name='Connectivity'),
        ),
    ]
