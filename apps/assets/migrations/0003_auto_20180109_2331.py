# Generated by Django 4.1.13 on 2024-05-09 03:16

import json
from functools import reduce

from django.db import migrations
from django.db.models import F

from assets.const import AllTypes

platforms_data_json = '''[
    {
        "created_by": null,
        "updated_by": null,
        "comment": "",
        "name": "Linux",
        "category": "host",
        "type": "linux",
        "meta": {},
        "internal": true,
        "domain_enabled": true,
        "su_enabled": true,
        "su_method": null,
        "custom_fields": [],
        "automation": {
            "ansible_enabled": true,
            "ansible_config": {
                "ansible_connection": "smart"
            },
            "ping_enabled": true,
            "ping_method": "posix_ping",
            "ping_params": {},
            "gather_facts_enabled": true,
            "gather_facts_method": "gather_facts_posix",
            "gather_facts_params": {},
            "change_secret_enabled": true,
            "change_secret_method": "change_secret_posix",
            "change_secret_params": {},
            "push_account_enabled": true,
            "push_account_method": "push_account_posix",
            "push_account_params": {
                "sudo": "/bin/whoami",
                "shell": "/bin/bash",
                "home": "",
                "groups": ""
            },
            "verify_account_enabled": true,
            "verify_account_method": "verify_account_posix",
            "verify_account_params": {},
            "gather_accounts_enabled": true,
            "gather_accounts_method": "gather_accounts_posix",
            "gather_accounts_params": {},
            "remove_account_enabled": true,
            "remove_account_method": "remove_account_posix",
            "remove_account_params": {}
        },
        "protocols": [
            {
                "name": "ssh",
                "port": 22,
                "primary": true,
                "required": false,
                "default": false,
                "public": true,
                "setting": {
                    "sftp_enabled": true,
                    "sftp_home": "/tmp"
                }
            },
            {
                "name": "telnet",
                "port": 23,
                "primary": false,
                "required": false,
                "default": false,
                "public": true,
                "setting": {}
            },
            {
                "name": "vnc",
                "port": 5900,
                "primary": false,
                "required": false,
                "default": false,
                "public": true,
                "setting": {}
            },
            {
                "name": "rdp",
                "port": 3389,
                "primary": false,
                "required": false,
                "default": false,
                "public": true,
                "setting": {
                    "console": false,
                    "security": "any"
                }
            },
            {
                "name": "sftp",
                "port": 22,
                "primary": false,
                "required": false,
                "default": true,
                "public": true,
                "setting": {
                    "sftp_home": "/tmp"
                }
            }
        ]
    },
    {
        "created_by": null,
        "updated_by": null,
        "comment": "",
        "name": "Unix",
        "category": "host",
        "type": "unix",
        "meta": {},
        "internal": true,
        "domain_enabled": true,
        "su_enabled": true,
        "su_method": null,
        "custom_fields": [],
        "automation": {
            "ansible_enabled": true,
            "ansible_config": {
                "ansible_connection": "smart"
            },
            "ping_enabled": true,
            "ping_method": "posix_ping",
            "ping_params": {},
            "gather_facts_enabled": true,
            "gather_facts_method": "gather_facts_posix",
            "gather_facts_params": {},
            "change_secret_enabled": true,
            "change_secret_method": "change_secret_posix",
            "change_secret_params": {},
            "push_account_enabled": true,
            "push_account_method": "push_account_posix",
            "push_account_params": {
                "sudo": "/bin/whoami",
                "shell": "/bin/bash",
                "home": "",
                "groups": ""
            },
            "verify_account_enabled": true,
            "verify_account_method": "verify_account_posix",
            "verify_account_params": {},
            "gather_accounts_enabled": true,
            "gather_accounts_method": "gather_accounts_posix",
            "gather_accounts_params": {},
            "remove_account_enabled": true,
            "remove_account_method": "remove_account_posix",
            "remove_account_params": {}
        },
        "protocols": [
            {
                "name": "ssh",
                "port": 22,
                "primary": true,
                "required": false,
                "default": false,
                "public": true,
                "setting": {
                    "sftp_enabled": true,
                    "sftp_home": "/tmp"
                }
            },
            {
                "name": "telnet",
                "port": 23,
                "primary": false,
                "required": false,
                "default": false,
                "public": true,
                "setting": {}
            },
            {
                "name": "vnc",
                "port": 5900,
                "primary": false,
                "required": false,
                "default": false,
                "public": true,
                "setting": {}
            },
            {
                "name": "rdp",
                "port": 3389,
                "primary": false,
                "required": false,
                "default": false,
                "public": true,
                "setting": {
                    "console": false,
                    "security": "any"
                }
            }
        ]
    },
    {
        "created_by": null,
        "updated_by": null,
        "comment": "",
        "name": "macOS",
        "category": "host",
        "type": "unix",
        "meta": {},
        "internal": true,
        "domain_enabled": true,
        "su_enabled": true,
        "su_method": null,
        "custom_fields": [],
        "automation": {
            "ansible_enabled": true,
            "ansible_config": {
                "ansible_connection": "smart"
            },
            "ping_enabled": true,
            "ping_method": "posix_ping",
            "ping_params": {},
            "gather_facts_enabled": true,
            "gather_facts_method": "gather_facts_posix",
            "gather_facts_params": {},
            "change_secret_enabled": true,
            "change_secret_method": "change_secret_posix",
            "change_secret_params": {},
            "push_account_enabled": true,
            "push_account_method": "push_account_posix",
            "push_account_params": {
                "sudo": "/bin/whoami",
                "shell": "/bin/bash",
                "home": "",
                "groups": ""
            },
            "verify_account_enabled": true,
            "verify_account_method": "verify_account_posix",
            "verify_account_params": {},
            "gather_accounts_enabled": true,
            "gather_accounts_method": "gather_accounts_posix",
            "gather_accounts_params": {},
            "remove_account_enabled": true,
            "remove_account_method": "remove_account_posix",
            "remove_account_params": {}
        },
        "protocols": [
            {
                "name": "ssh",
                "port": 22,
                "primary": true,
                "required": false,
                "default": false,
                "public": true,
                "setting": {
                    "sftp_enabled": true,
                    "sftp_home": "/tmp"
                }
            },
            {
                "name": "telnet",
                "port": 23,
                "primary": false,
                "required": false,
                "default": false,
                "public": true,
                "setting": {}
            },
            {
                "name": "vnc",
                "port": 5900,
                "primary": false,
                "required": false,
                "default": false,
                "public": true,
                "setting": {}
            },
            {
                "name": "rdp",
                "port": 3389,
                "primary": false,
                "required": false,
                "default": false,
                "public": true,
                "setting": {
                    "console": false,
                    "security": "any"
                }
            }
        ]
    },
    {
        "created_by": null,
        "updated_by": null,
        "comment": "",
        "name": "BSD",
        "category": "host",
        "type": "unix",
        "meta": {},
        "internal": true,
        "domain_enabled": true,
        "su_enabled": true,
        "su_method": null,
        "custom_fields": [],
        "automation": {
            "ansible_enabled": true,
            "ansible_config": {
                "ansible_connection": "smart"
            },
            "ping_enabled": true,
            "ping_method": "posix_ping",
            "ping_params": {},
            "gather_facts_enabled": true,
            "gather_facts_method": "gather_facts_posix",
            "gather_facts_params": {},
            "change_secret_enabled": true,
            "change_secret_method": "change_secret_posix",
            "change_secret_params": {},
            "push_account_enabled": true,
            "push_account_method": "push_account_posix",
            "push_account_params": {
                "sudo": "/bin/whoami",
                "shell": "/bin/bash",
                "home": "",
                "groups": ""
            },
            "verify_account_enabled": true,
            "verify_account_method": "verify_account_posix",
            "verify_account_params": {},
            "gather_accounts_enabled": true,
            "gather_accounts_method": "gather_accounts_posix",
            "gather_accounts_params": {},
            "remove_account_enabled": true,
            "remove_account_method": "remove_account_posix",
            "remove_account_params": {}
        },
        "protocols": [
            {
                "name": "ssh",
                "port": 22,
                "primary": true,
                "required": false,
                "default": false,
                "public": true,
                "setting": {
                    "sftp_enabled": true,
                    "sftp_home": "/tmp"
                }
            },
            {
                "name": "telnet",
                "port": 23,
                "primary": false,
                "required": false,
                "default": false,
                "public": true,
                "setting": {}
            },
            {
                "name": "vnc",
                "port": 5900,
                "primary": false,
                "required": false,
                "default": false,
                "public": true,
                "setting": {}
            },
            {
                "name": "rdp",
                "port": 3389,
                "primary": false,
                "required": false,
                "default": false,
                "public": true,
                "setting": {
                    "console": false,
                    "security": "any"
                }
            }
        ]
    },
    {
        "created_by": null,
        "updated_by": null,
        "comment": "",
        "name": "Windows",
        "category": "host",
        "type": "windows",
        "meta": {},
        "internal": true,
        "domain_enabled": true,
        "su_enabled": false,
        "su_method": null,
        "custom_fields": [],
        "automation": {
            "ansible_enabled": true,
            "ansible_config": {
                "ansible_shell_type": "cmd",
                "ansible_connection": "ssh"
            },
            "ping_enabled": true,
            "ping_method": "ping_by_rdp",
            "ping_params": {},
            "gather_facts_enabled": true,
            "gather_facts_method": "gather_facts_windows",
            "gather_facts_params": {},
            "change_secret_enabled": true,
            "change_secret_method": "change_secret_local_windows",
            "change_secret_params": {},
            "push_account_enabled": true,
            "push_account_method": "push_account_local_windows",
            "push_account_params": {
                "groups": "Users,Remote Desktop Users"
            },
            "verify_account_enabled": true,
            "verify_account_method": "verify_account_by_rdp",
            "verify_account_params": {},
            "gather_accounts_enabled": true,
            "gather_accounts_method": "gather_accounts_windows",
            "gather_accounts_params": {},
            "remove_account_enabled": true,
            "remove_account_method": "remove_account_windows",
            "remove_account_params": {}
        },
        "protocols": [
            {
                "name": "rdp",
                "port": 3389,
                "primary": true,
                "required": false,
                "default": false,
                "public": true,
                "setting": {
                    "console": false,
                    "security": "any"
                }
            },
            {
                "name": "ssh",
                "port": 22,
                "primary": false,
                "required": false,
                "default": false,
                "public": true,
                "setting": {
                    "sftp_enabled": true,
                    "sftp_home": "/tmp"
                }
            },
            {
                "name": "vnc",
                "port": 5900,
                "primary": false,
                "required": false,
                "default": false,
                "public": true,
                "setting": {}
            },
            {
                "name": "winrm",
                "port": 5985,
                "primary": false,
                "required": false,
                "default": false,
                "public": false,
                "setting": {
                    "use_ssl": false
                }
            }
        ]
    },
    {
        "created_by": null,
        "updated_by": null,
        "comment": "",
        "name": "Windows2016",
        "category": "host",
        "type": "windows",
        "meta": {
            "security": "any"
        },
        "internal": false,
        "domain_enabled": true,
        "su_enabled": false,
        "su_method": null,
        "custom_fields": [],
        "automation": {
            "ansible_enabled": true,
            "ansible_config": {
                "ansible_shell_type": "cmd",
                "ansible_connection": "smart"
            },
            "ping_enabled": true,
            "ping_method": "ping_by_rdp",
            "ping_params": {},
            "gather_facts_enabled": true,
            "gather_facts_method": "gather_facts_windows",
            "gather_facts_params": {},
            "change_secret_enabled": true,
            "change_secret_method": "change_secret_local_windows",
            "change_secret_params": {},
            "push_account_enabled": true,
            "push_account_method": "push_account_local_windows",
            "push_account_params": {
                "groups": "Users,Remote Desktop Users"
            },
            "verify_account_enabled": true,
            "verify_account_method": "verify_account_by_rdp",
            "verify_account_params": {},
            "gather_accounts_enabled": true,
            "gather_accounts_method": "gather_accounts_windows",
            "gather_accounts_params": {},
            "remove_account_enabled": true,
            "remove_account_method": "remove_account_windows",
            "remove_account_params": {}
        },
        "protocols": [
            {
                "name": "rdp",
                "port": 3389,
                "primary": true,
                "required": false,
                "default": true,
                "public": true,
                "setting": {
                    "console": false,
                    "security": "any",
                    "ad_domain": ""
                }
            },
            {
                "name": "ssh",
                "port": 22,
                "primary": false,
                "required": false,
                "default": true,
                "public": true,
                "setting": {
                    "old_ssh_version": false
                }
            },
            {
                "name": "sftp",
                "port": 22,
                "primary": false,
                "required": false,
                "default": true,
                "public": true,
                "setting": {
                    "sftp_home": "/tmp"
                }
            },
            {
                "name": "vnc",
                "port": 5900,
                "primary": false,
                "required": false,
                "default": true,
                "public": true,
                "setting": {}
            },
            {
                "name": "winrm",
                "port": 5985,
                "primary": false,
                "required": false,
                "default": true,
                "public": true,
                "setting": {
                    "use_ssl": false
                }
            }
        ]
    },
    {
        "created_by": null,
        "updated_by": null,
        "comment": "",
        "name": "Other",
        "category": "host",
        "type": "other",
        "meta": {},
        "internal": false,
        "domain_enabled": true,
        "su_enabled": false,
        "su_method": null,
        "custom_fields": [],
        "automation": {
            "ansible_enabled": false,
            "ansible_config": {
                "ansible_connection": "smart"
            },
            "ping_enabled": false,
            "ping_method": null,
            "ping_params": {},
            "gather_facts_enabled": false,
            "gather_facts_method": null,
            "gather_facts_params": {},
            "change_secret_enabled": false,
            "change_secret_method": null,
            "change_secret_params": {},
            "push_account_enabled": false,
            "push_account_method": null,
            "push_account_params": {},
            "verify_account_enabled": false,
            "verify_account_method": null,
            "verify_account_params": {},
            "gather_accounts_enabled": false,
            "gather_accounts_method": null,
            "gather_accounts_params": {},
            "remove_account_enabled": false,
            "remove_account_method": null,
            "remove_account_params": {}
        },
        "protocols": [
            {
                "name": "ssh",
                "port": 22,
                "primary": true,
                "required": false,
                "default": true,
                "public": true,
                "setting": {
                    "old_ssh_version": false
                }
            },
            {
                "name": "sftp",
                "port": 22,
                "primary": false,
                "required": false,
                "default": true,
                "public": true,
                "setting": {
                    "sftp_home": "/tmp"
                }
            },
            {
                "name": "telnet",
                "port": 23,
                "primary": false,
                "required": false,
                "default": true,
                "public": true,
                "setting": {
                    "username_prompt": "username:|login:",
                    "password_prompt": "password:",
                    "success_prompt": "success|成功|#|>"
                }
            },
            {
                "name": "vnc",
                "port": 5900,
                "primary": false,
                "required": false,
                "default": true,
                "public": true,
                "setting": {}
            },
            {
                "name": "rdp",
                "port": 3389,
                "primary": false,
                "required": false,
                "default": true,
                "public": true,
                "setting": {
                    "console": false,
                    "security": "any",
                    "ad_domain": ""
                }
            }
        ]
    },
    {
        "created_by": null,
        "updated_by": null,
        "comment": "",
        "name": "Windows-RDP",
        "category": "host",
        "type": "windows",
        "meta": {
            "security": "rdp"
        },
        "internal": true,
        "domain_enabled": true,
        "su_enabled": false,
        "su_method": null,
        "custom_fields": [],
        "automation": {
            "ansible_enabled": true,
            "ansible_config": {
                "ansible_shell_type": "cmd",
                "ansible_connection": "ssh"
            },
            "ping_enabled": true,
            "ping_method": "win_ping",
            "ping_params": {},
            "gather_facts_enabled": true,
            "gather_facts_method": "gather_facts_windows",
            "gather_facts_params": {},
            "change_secret_enabled": true,
            "change_secret_method": "change_secret_local_windows",
            "change_secret_params": {},
            "push_account_enabled": true,
            "push_account_method": "push_account_local_windows",
            "push_account_params": {
                "groups": "Users,Remote Desktop Users"
            },
            "verify_account_enabled": true,
            "verify_account_method": "verify_account_windows",
            "verify_account_params": {},
            "gather_accounts_enabled": true,
            "gather_accounts_method": "gather_accounts_windows",
            "gather_accounts_params": {},
            "remove_account_enabled": true,
            "remove_account_method": "remove_account_windows",
            "remove_account_params": {}
        },
        "protocols": [
            {
                "name": "rdp",
                "port": 3389,
                "primary": true,
                "required": false,
                "default": false,
                "public": true,
                "setting": {
                    "console": false,
                    "security": "rdp"
                }
            },
            {
                "name": "ssh",
                "port": 22,
                "primary": false,
                "required": false,
                "default": false,
                "public": true,
                "setting": {
                    "sftp_enabled": true,
                    "sftp_home": "/tmp"
                }
            },
            {
                "name": "vnc",
                "port": 5900,
                "primary": false,
                "required": false,
                "default": false,
                "public": true,
                "setting": {}
            },
            {
                "name": "winrm",
                "port": 5985,
                "primary": false,
                "required": false,
                "default": false,
                "public": false,
                "setting": {
                    "use_ssl": false
                }
            }
        ]
    },
    {
        "created_by": null,
        "updated_by": null,
        "comment": "",
        "name": "Windows-TLS",
        "category": "host",
        "type": "windows",
        "meta": {
            "security": "tls"
        },
        "internal": true,
        "domain_enabled": true,
        "su_enabled": false,
        "su_method": null,
        "custom_fields": [],
        "automation": {
            "ansible_enabled": true,
            "ansible_config": {
                "ansible_shell_type": "cmd",
                "ansible_connection": "ssh"
            },
            "ping_enabled": true,
            "ping_method": "win_ping",
            "ping_params": {},
            "gather_facts_enabled": true,
            "gather_facts_method": "gather_facts_windows",
            "gather_facts_params": {},
            "change_secret_enabled": true,
            "change_secret_method": "change_secret_local_windows",
            "change_secret_params": {},
            "push_account_enabled": true,
            "push_account_method": "push_account_local_windows",
            "push_account_params": {
                "groups": "Users,Remote Desktop Users"
            },
            "verify_account_enabled": true,
            "verify_account_method": "verify_account_windows",
            "verify_account_params": {},
            "gather_accounts_enabled": true,
            "gather_accounts_method": "gather_accounts_windows",
            "gather_accounts_params": {},
            "remove_account_enabled": true,
            "remove_account_method": "remove_account_windows",
            "remove_account_params": {}
        },
        "protocols": [
            {
                "name": "rdp",
                "port": 3389,
                "primary": true,
                "required": false,
                "default": false,
                "public": true,
                "setting": {
                    "console": false,
                    "security": "tls"
                }
            },
            {
                "name": "ssh",
                "port": 22,
                "primary": false,
                "required": false,
                "default": false,
                "public": true,
                "setting": {
                    "sftp_enabled": true,
                    "sftp_home": "/tmp"
                }
            },
            {
                "name": "vnc",
                "port": 5900,
                "primary": false,
                "required": false,
                "default": false,
                "public": true,
                "setting": {}
            },
            {
                "name": "winrm",
                "port": 5985,
                "primary": false,
                "required": false,
                "default": false,
                "public": false,
                "setting": {
                    "use_ssl": false
                }
            }
        ]
    },
    {
        "created_by": null,
        "updated_by": null,
        "comment": "",
        "name": "AIX",
        "category": "host",
        "type": "unix",
        "meta": {},
        "internal": true,
        "domain_enabled": true,
        "su_enabled": true,
        "su_method": null,
        "custom_fields": [],
        "automation": {
            "ansible_enabled": true,
            "ansible_config": {
                "ansible_connection": "smart"
            },
            "ping_enabled": true,
            "ping_method": "posix_ping",
            "ping_params": {},
            "gather_facts_enabled": true,
            "gather_facts_method": "gather_facts_posix",
            "gather_facts_params": {},
            "change_secret_enabled": true,
            "change_secret_method": "change_secret_aix",
            "change_secret_params": {},
            "push_account_enabled": true,
            "push_account_method": "push_account_aix",
            "push_account_params": {
                "sudo": "/bin/whoami",
                "shell": "/bin/bash",
                "home": "",
                "groups": ""
            },
            "verify_account_enabled": true,
            "verify_account_method": "verify_account_posix",
            "verify_account_params": {},
            "gather_accounts_enabled": true,
            "gather_accounts_method": "gather_accounts_posix",
            "gather_accounts_params": {},
            "remove_account_enabled": true,
            "remove_account_method": "remove_account_posix",
            "remove_account_params": {}
        },
        "protocols": [
            {
                "name": "ssh",
                "port": 22,
                "primary": true,
                "required": false,
                "default": false,
                "public": true,
                "setting": {
                    "sftp_enabled": true,
                    "sftp_home": "/tmp"
                }
            },
            {
                "name": "telnet",
                "port": 23,
                "primary": false,
                "required": false,
                "default": false,
                "public": true,
                "setting": {}
            },
            {
                "name": "vnc",
                "port": 5900,
                "primary": false,
                "required": false,
                "default": false,
                "public": true,
                "setting": {}
            },
            {
                "name": "rdp",
                "port": 3389,
                "primary": false,
                "required": false,
                "default": false,
                "public": true,
                "setting": {
                    "console": false,
                    "security": "any"
                }
            }
        ]
    },
    {
        "created_by": null,
        "updated_by": null,
        "comment": "",
        "name": "Gateway",
        "category": "host",
        "type": "linux",
        "meta": {},
        "internal": true,
        "domain_enabled": true,
        "su_enabled": true,
        "su_method": null,
        "custom_fields": [],
        "automation": {
            "ansible_enabled": true,
            "ansible_config": {
                "ansible_connection": "smart"
            },
            "ping_enabled": true,
            "ping_method": "posix_ping",
            "ping_params": {},
            "gather_facts_enabled": true,
            "gather_facts_method": "gather_facts_posix",
            "gather_facts_params": {},
            "change_secret_enabled": true,
            "change_secret_method": "change_secret_posix",
            "change_secret_params": {},
            "push_account_enabled": true,
            "push_account_method": "push_account_posix",
            "push_account_params": {
                "sudo": "/bin/whoami",
                "shell": "/bin/bash",
                "home": "",
                "groups": ""
            },
            "verify_account_enabled": true,
            "verify_account_method": "verify_account_posix",
            "verify_account_params": {},
            "gather_accounts_enabled": true,
            "gather_accounts_method": "gather_accounts_posix",
            "gather_accounts_params": {},
            "remove_account_enabled": true,
            "remove_account_method": "remove_account_posix",
            "remove_account_params": {}
        },
        "protocols": [
            {
                "name": "ssh",
                "port": 22,
                "primary": true,
                "required": false,
                "default": false,
                "public": true,
                "setting": {
                    "sftp_enabled": true,
                    "sftp_home": "/tmp"
                }
            },
            {
                "name": "telnet",
                "port": 23,
                "primary": false,
                "required": false,
                "default": false,
                "public": true,
                "setting": {}
            },
            {
                "name": "vnc",
                "port": 5900,
                "primary": false,
                "required": false,
                "default": false,
                "public": true,
                "setting": {}
            },
            {
                "name": "rdp",
                "port": 3389,
                "primary": false,
                "required": false,
                "default": false,
                "public": true,
                "setting": {
                    "console": false,
                    "security": "any"
                }
            },
            {
                "name": "sftp",
                "port": 22,
                "primary": false,
                "required": false,
                "default": true,
                "public": true,
                "setting": {
                    "sftp_home": "/tmp"
                }
            }
        ]
    },
    {
        "created_by": null,
        "updated_by": null,
        "comment": "",
        "name": "RemoteAppHost",
        "category": "host",
        "type": "windows",
        "meta": {},
        "internal": true,
        "domain_enabled": true,
        "su_enabled": false,
        "su_method": null,
        "custom_fields": [],
        "automation": {
            "ansible_enabled": true,
            "ansible_config": {
                "ansible_shell_type": "cmd",
                "ansible_connection": "ssh"
            },
            "ping_enabled": true,
            "ping_method": "win_ping",
            "ping_params": {},
            "gather_facts_enabled": true,
            "gather_facts_method": "gather_facts_windows",
            "gather_facts_params": {},
            "change_secret_enabled": true,
            "change_secret_method": "change_secret_local_windows",
            "change_secret_params": {},
            "push_account_enabled": true,
            "push_account_method": "push_account_local_windows",
            "push_account_params": {
                "groups": "Users,Remote Desktop Users"
            },
            "verify_account_enabled": true,
            "verify_account_method": "verify_account_windows",
            "verify_account_params": {},
            "gather_accounts_enabled": true,
            "gather_accounts_method": "gather_accounts_windows",
            "gather_accounts_params": {},
            "remove_account_enabled": true,
            "remove_account_method": "remove_account_windows",
            "remove_account_params": {}
        },
        "protocols": [
            {
                "name": "rdp",
                "port": 3389,
                "primary": true,
                "required": false,
                "default": false,
                "public": true,
                "setting": {
                    "console": false,
                    "security": "any"
                }
            },
            {
                "name": "ssh",
                "port": 22,
                "primary": false,
                "required": false,
                "default": true,
                "public": true,
                "setting": {
                    "sftp_enabled": true,
                    "sftp_home": "/tmp"
                }
            },
            {
                "name": "winrm",
                "port": 5985,
                "primary": false,
                "required": false,
                "default": false,
                "public": false,
                "setting": {}
            }
        ]
    },
    {
        "created_by": null,
        "updated_by": null,
        "comment": "",
        "name": "General",
        "category": "device",
        "type": "general",
        "meta": {},
        "internal": true,
        "domain_enabled": true,
        "su_enabled": false,
        "su_method": null,
        "custom_fields": [],
        "automation": {
            "ansible_enabled": true,
            "ansible_config": {
                "ansible_connection": "local",
                "first_connect_delay": 0.5
            },
            "ping_enabled": true,
            "ping_method": "ping_by_ssh",
            "ping_params": {},
            "gather_facts_enabled": false,
            "gather_facts_method": null,
            "gather_facts_params": {},
            "change_secret_enabled": true,
            "change_secret_method": "change_secret_by_ssh",
            "change_secret_params": {},
            "push_account_enabled": false,
            "push_account_method": null,
            "push_account_params": {},
            "verify_account_enabled": true,
            "verify_account_method": "verify_account_by_ssh",
            "verify_account_params": {},
            "gather_accounts_enabled": false,
            "gather_accounts_method": null,
            "gather_accounts_params": {},
            "remove_account_enabled": false,
            "remove_account_method": null,
            "remove_account_params": {}
        },
        "protocols": [
            {
                "name": "ssh",
                "port": 22,
                "primary": true,
                "required": false,
                "default": false,
                "public": true,
                "setting": {
                    "sftp_enabled": true,
                    "sftp_home": "/tmp"
                }
            },
            {
                "name": "telnet",
                "port": 23,
                "primary": false,
                "required": false,
                "default": false,
                "public": true,
                "setting": {}
            }
        ]
    },
    {
        "created_by": null,
        "updated_by": null,
        "comment": "",
        "name": "Cisco",
        "category": "device",
        "type": "general",
        "meta": {},
        "internal": true,
        "domain_enabled": true,
        "su_enabled": true,
        "su_method": "enable",
        "custom_fields": [],
        "automation": {
            "ansible_enabled": true,
            "ansible_config": {
                "ansible_connection": "local",
                "first_connect_delay": 0.5
            },
            "ping_enabled": true,
            "ping_method": "ping_by_ssh",
            "ping_params": {},
            "gather_facts_enabled": false,
            "gather_facts_method": null,
            "gather_facts_params": {},
            "change_secret_enabled": true,
            "change_secret_method": "change_secret_by_ssh",
            "change_secret_params": {},
            "push_account_enabled": false,
            "push_account_method": null,
            "push_account_params": {},
            "verify_account_enabled": true,
            "verify_account_method": "verify_account_by_ssh",
            "verify_account_params": {},
            "gather_accounts_enabled": false,
            "gather_accounts_method": null,
            "gather_accounts_params": {},
            "remove_account_enabled": false,
            "remove_account_method": null,
            "remove_account_params": {}
        },
        "protocols": [
            {
                "name": "ssh",
                "port": 22,
                "primary": true,
                "required": false,
                "default": false,
                "public": true,
                "setting": {
                    "sftp_enabled": true,
                    "sftp_home": "/tmp"
                }
            },
            {
                "name": "telnet",
                "port": 23,
                "primary": false,
                "required": false,
                "default": false,
                "public": true,
                "setting": {}
            }
        ]
    },
    {
        "created_by": null,
        "updated_by": null,
        "comment": "",
        "name": "Huawei",
        "category": "device",
        "type": "general",
        "meta": {},
        "internal": true,
        "domain_enabled": true,
        "su_enabled": true,
        "su_method": "super",
        "custom_fields": [],
        "automation": {
            "ansible_enabled": true,
            "ansible_config": {
                "ansible_connection": "network_cli",
                "first_connect_delay": 0.5,
                "ansible_network_os":"ce"
            },
            "ping_enabled": true,
            "ping_method": "ping_by_ssh",
            "ping_params": {},
            "gather_facts_enabled": false,
            "gather_facts_method": null,
            "gather_facts_params": {},
            "change_secret_enabled": true,
            "change_secret_method": "change_secret_by_ssh",
            "change_secret_params": {},
            "push_account_enabled": false,
            "push_account_method": null,
            "push_account_params": {},
            "verify_account_enabled": true,
            "verify_account_method": "verify_account_by_ssh",
            "verify_account_params": {},
            "gather_accounts_enabled": false,
            "gather_accounts_method": null,
            "gather_accounts_params": {},
            "remove_account_enabled": false,
            "remove_account_method": null,
            "remove_account_params": {}
        },
        "protocols": [
            {
                "name": "ssh",
                "port": 22,
                "primary": true,
                "required": false,
                "default": false,
                "public": true,
                "setting": {
                    "sftp_enabled": true,
                    "sftp_home": "/tmp"
                }
            },
            {
                "name": "telnet",
                "port": 23,
                "primary": false,
                "required": false,
                "default": false,
                "public": true,
                "setting": {}
            }
        ]
    },
    {
        "created_by": null,
        "updated_by": null,
        "comment": "",
        "name": "H3C",
        "category": "device",
        "type": "general",
        "meta": {},
        "internal": true,
        "domain_enabled": true,
        "su_enabled": true,
        "su_method": "super_level",
        "custom_fields": [],
        "automation": {
            "ansible_enabled": true,
            "ansible_config": {
                "ansible_connection": "local",
                "first_connect_delay": 0.5
            },
            "ping_enabled": true,
            "ping_method": "ping_by_ssh",
            "ping_params": {},
            "gather_facts_enabled": false,
            "gather_facts_method": null,
            "gather_facts_params": {},
            "change_secret_enabled": true,
            "change_secret_method": "change_secret_by_ssh",
            "change_secret_params": {},
            "push_account_enabled": false,
            "push_account_method": null,
            "push_account_params": {},
            "verify_account_enabled": true,
            "verify_account_method": "verify_account_by_ssh",
            "verify_account_params": {},
            "gather_accounts_enabled": false,
            "gather_accounts_method": null,
            "gather_accounts_params": {},
            "remove_account_enabled": false,
            "remove_account_method": null,
            "remove_account_params": {}
        },
        "protocols": [
            {
                "name": "ssh",
                "port": 22,
                "primary": true,
                "required": false,
                "default": false,
                "public": true,
                "setting": {
                    "sftp_enabled": true,
                    "sftp_home": "/tmp"
                }
            },
            {
                "name": "telnet",
                "port": 23,
                "primary": false,
                "required": false,
                "default": false,
                "public": true,
                "setting": {}
            }
        ]
    },
    {
        "created_by": null,
        "updated_by": null,
        "comment": "",
        "name": "MySQL",
        "category": "database",
        "type": "mysql",
        "meta": {},
        "internal": true,
        "domain_enabled": true,
        "su_enabled": false,
        "su_method": null,
        "custom_fields": [],
        "automation": {
            "ansible_enabled": true,
            "ansible_config": {
                "ansible_connection": "local"
            },
            "ping_enabled": true,
            "ping_method": "mysql_ping",
            "ping_params": {},
            "gather_facts_enabled": false,
            "gather_facts_method": null,
            "gather_facts_params": {},
            "change_secret_enabled": true,
            "change_secret_method": "change_secret_mysql",
            "change_secret_params": {},
            "push_account_enabled": true,
            "push_account_method": "push_account_mysql",
            "push_account_params": {},
            "verify_account_enabled": true,
            "verify_account_method": "verify_account_mysql",
            "verify_account_params": {},
            "gather_accounts_enabled": true,
            "gather_accounts_method": "gather_accounts_mysql",
            "gather_accounts_params": {},
            "remove_account_enabled": true,
            "remove_account_method": "remove_account_mysql",
            "remove_account_params": {}
        },
        "protocols": [
            {
                "name": "mysql",
                "port": 3306,
                "primary": true,
                "required": false,
                "default": false,
                "public": true,
                "setting": {}
            }
        ]
    },
    {
        "created_by": null,
        "updated_by": null,
        "comment": "",
        "name": "MariaDB",
        "category": "database",
        "type": "mariadb",
        "meta": {},
        "internal": true,
        "domain_enabled": true,
        "su_enabled": false,
        "su_method": null,
        "custom_fields": [],
        "automation": {
            "ansible_enabled": true,
            "ansible_config": {
                "ansible_connection": "local"
            },
            "ping_enabled": true,
            "ping_method": "mysql_ping",
            "ping_params": {},
            "gather_facts_enabled": false,
            "gather_facts_method": null,
            "gather_facts_params": {},
            "change_secret_enabled": true,
            "change_secret_method": "change_secret_mysql",
            "change_secret_params": {},
            "push_account_enabled": true,
            "push_account_method": "push_account_mysql",
            "push_account_params": {},
            "verify_account_enabled": true,
            "verify_account_method": "verify_account_mysql",
            "verify_account_params": {},
            "gather_accounts_enabled": true,
            "gather_accounts_method": "gather_accounts_mysql",
            "gather_accounts_params": {},
            "remove_account_enabled": true,
            "remove_account_method": "remove_account_mysql",
            "remove_account_params": {}
        },
        "protocols": [
            {
                "name": "mariadb",
                "port": 3306,
                "primary": true,
                "required": false,
                "default": false,
                "public": true,
                "setting": {}
            }
        ]
    },
    {
        "created_by": null,
        "updated_by": null,
        "comment": "",
        "name": "PostgreSQL",
        "category": "database",
        "type": "postgresql",
        "meta": {},
        "internal": true,
        "domain_enabled": true,
        "su_enabled": false,
        "su_method": null,
        "custom_fields": [],
        "automation": {
            "ansible_enabled": true,
            "ansible_config": {
                "ansible_connection": "local"
            },
            "ping_enabled": true,
            "ping_method": "ping_postgresql",
            "ping_params": {},
            "gather_facts_enabled": false,
            "gather_facts_method": null,
            "gather_facts_params": {},
            "change_secret_enabled": true,
            "change_secret_method": "change_secret_postgresql",
            "change_secret_params": {},
            "push_account_enabled": true,
            "push_account_method": "push_account_postgresql",
            "push_account_params": {},
            "verify_account_enabled": true,
            "verify_account_method": "verify_account_postgresql",
            "verify_account_params": {},
            "gather_accounts_enabled": true,
            "gather_accounts_method": "gather_accounts_postgresql",
            "gather_accounts_params": {},
            "remove_account_enabled": true,
            "remove_account_method": "remove_account_postgresql",
            "remove_account_params": {}
        },
        "protocols": [
            {
                "name": "postgresql",
                "port": 5432,
                "primary": true,
                "required": false,
                "default": false,
                "public": true,
                "setting": {}
            }
        ]
    },
    {
        "created_by": null,
        "updated_by": null,
        "comment": "",
        "name": "Oracle",
        "category": "database",
        "type": "oracle",
        "meta": {},
        "internal": true,
        "domain_enabled": true,
        "su_enabled": false,
        "su_method": null,
        "custom_fields": [],
        "automation": {
            "ansible_enabled": true,
            "ansible_config": {
                "ansible_connection": "local"
            },
            "ping_enabled": true,
            "ping_method": "oracle_ping",
            "ping_params": {},
            "gather_facts_enabled": false,
            "gather_facts_method": null,
            "gather_facts_params": {},
            "change_secret_enabled": true,
            "change_secret_method": "change_secret_oracle",
            "change_secret_params": {},
            "push_account_enabled": true,
            "push_account_method": "push_account_oracle",
            "push_account_params": {},
            "verify_account_enabled": true,
            "verify_account_method": "verify_account_oracle",
            "verify_account_params": {},
            "gather_accounts_enabled": true,
            "gather_accounts_method": "gather_accounts_oracle",
            "gather_accounts_params": {},
            "remove_account_enabled": true,
            "remove_account_method": "remove_account_oracle",
            "remove_account_params": {}
        },
        "protocols": [
            {
                "name": "oracle",
                "port": 1521,
                "primary": true,
                "required": false,
                "default": false,
                "public": true,
                "setting": {}
            }
        ]
    },
    {
        "created_by": null,
        "updated_by": null,
        "comment": "",
        "name": "SQLServer",
        "category": "database",
        "type": "sqlserver",
        "meta": {},
        "internal": true,
        "domain_enabled": true,
        "su_enabled": false,
        "su_method": null, 
        "custom_fields": [],
        "automation": {
            "ansible_enabled": true,
            "ansible_config": {
                "ansible_connection": "local"
            },
            "ping_enabled": true,
            "ping_method": "sqlserver_ping",
            "ping_params": {},
            "gather_facts_enabled": false,
            "gather_facts_method": null,
            "gather_facts_params": {},
            "change_secret_enabled": true,
            "change_secret_method": "change_secret_sqlserver",
            "change_secret_params": {},
            "push_account_enabled": true,
            "push_account_method": "push_account_sqlserver",
            "push_account_params": {},
            "verify_account_enabled": true,
            "verify_account_method": "verify_account_sqlserver",
            "verify_account_params": {},
            "gather_accounts_enabled": true,
            "gather_accounts_method": null,
            "gather_accounts_params": {},
            "remove_account_enabled": true,
            "remove_account_method": "remove_account_sqlserver",
            "remove_account_params": {}
        },
        "protocols": [
            {
                "name": "sqlserver",
                "port": 1433,
                "primary": true,
                "required": false,
                "default": false,
                "public": true,
                "setting": {}
            }
        ]
    },
    {
        "created_by": null,
        "updated_by": null,
        "comment": "",
        "name": "ClickHouse",
        "category": "database",
        "type": "clickhouse",
        "meta": {},
        "internal": true,
        "domain_enabled": true,
        "su_enabled": false,
        "su_method": null,
        "custom_fields": [],
        "automation": {
            "ansible_enabled": false,
            "ansible_config": {
                "ansible_connection": "local"
            },
            "ping_enabled": false,
            "ping_method": null,
            "ping_params": {},
            "gather_facts_enabled": false,
            "gather_facts_method": null,
            "gather_facts_params": {},
            "change_secret_enabled": false,
            "change_secret_method": null,
            "change_secret_params": {},
            "push_account_enabled": false,
            "push_account_method": null,
            "push_account_params": {},
            "verify_account_enabled": false,
            "verify_account_method": null,
            "verify_account_params": {},
            "gather_accounts_enabled": false,
            "gather_accounts_method": null,
            "gather_accounts_params": {},
            "remove_account_enabled": false,
            "remove_account_method": null,
            "remove_account_params": {}
        },
        "protocols": [
            {
                "name": "clickhouse",
                "port": 9000,
                "primary": true,
                "required": false,
                "default": false,
                "public": true,
                "setting": {}
            }
        ]
    },
    {
        "created_by": null,
        "updated_by": null,
        "comment": "",
        "name": "MongoDB",
        "category": "database",
        "type": "mongodb",
        "meta": {},
        "internal": true,
        "domain_enabled": true,
        "su_enabled": false,
        "su_method": null,
        "custom_fields": [],
        "automation": {
            "ansible_enabled": true,
            "ansible_config": {
                "ansible_connection": "local"
            },
            "ping_enabled": true,
            "ping_method": "mongodb_ping",
            "ping_params": {},
            "gather_facts_enabled": false,
            "gather_facts_method": null,
            "gather_facts_params": {},
            "change_secret_enabled": true,
            "change_secret_method": "change_secret_mongodb",
            "change_secret_params": {},
            "push_account_enabled": true,
            "push_account_method": "push_account_mongodb",
            "push_account_params": {},
            "verify_account_enabled": true,
            "verify_account_method": "verify_account_mongodb",
            "verify_account_params": {},
            "gather_accounts_enabled": true,
            "gather_accounts_method": "gather_accounts_mongodb",
            "gather_accounts_params": {},
            "remove_account_enabled": true,
            "remove_account_method": "remove_account_mongodb",
            "remove_account_params": {}
        },
        "protocols": [
            {
                "name": "mongodb",
                "port": 27017,
                "primary": true,
                "required": false,
                "default": false,
                "public": true,
                "setting": {}
            }
        ]
    },
    {
        "created_by": null,
        "updated_by": null,
        "comment": "",
        "name": "Redis",
        "category": "database",
        "type": "redis",
        "meta": {},
        "internal": true,
        "domain_enabled": true,
        "su_enabled": false,
        "su_method": null,
        "custom_fields": [],
        "automation": {
            "ansible_enabled": false,
            "ansible_config": {
                "ansible_connection": "local"
            },
            "ping_enabled": false,
            "ping_method": null,
            "ping_params": {},
            "gather_facts_enabled": false,
            "gather_facts_method": null,
            "gather_facts_params": {},
            "change_secret_enabled": false,
            "change_secret_method": null,
            "change_secret_params": {},
            "push_account_enabled": false,
            "push_account_method": null,
            "push_account_params": {},
            "verify_account_enabled": false,
            "verify_account_method": null,
            "verify_account_params": {},
            "gather_accounts_enabled": false,
            "gather_accounts_method": null,
            "gather_accounts_params": {},
            "remove_account_enabled": false,
            "remove_account_method": null,
            "remove_account_params": {}
        },
        "protocols": [
            {
                "name": "redis",
                "port": 6379,
                "primary": true,
                "required": false,
                "default": false,
                "public": true,
                "setting": {
                    "auth_username": false
                }
            }
        ]
    },
    {
        "created_by": null,
        "updated_by": null,
        "comment": "",
        "name": "Redis6+",
        "category": "database",
        "type": "redis",
        "meta": {},
        "internal": true,
        "domain_enabled": true,
        "su_enabled": false,
        "su_method": null,
        "custom_fields": [],
        "automation": {
            "ansible_enabled": false,
            "ansible_config": {
                "ansible_connection": "local"
            },
            "ping_enabled": false,
            "ping_method": null,
            "ping_params": {},
            "gather_facts_enabled": false,
            "gather_facts_method": null,
            "gather_facts_params": {},
            "change_secret_enabled": false,
            "change_secret_method": null,
            "change_secret_params": {},
            "push_account_enabled": false,
            "push_account_method": null,
            "push_account_params": {},
            "verify_account_enabled": false,
            "verify_account_method": null,
            "verify_account_params": {},
            "gather_accounts_enabled": false,
            "gather_accounts_method": null,
            "gather_accounts_params": {},
            "remove_account_enabled": false,
            "remove_account_method": null,
            "remove_account_params": {}
        },
        "protocols": [
            {
                "name": "redis",
                "port": 6379,
                "primary": true,
                "required": false,
                "default": false,
                "public": true,
                "setting": {
                    "auth_username": true
                }
            }
        ]
    },
    {
        "created_by": null,
        "updated_by": null,
        "comment": "",
        "name": "Website",
        "category": "web",
        "type": "website",
        "meta": {},
        "internal": true,
        "domain_enabled": false,
        "su_enabled": false,
        "su_method": null,
        "custom_fields": [],
        "automation": {
            "ansible_enabled": false,
            "ansible_config": {},
            "ping_enabled": false,
            "ping_method": null,
            "ping_params": {},
            "gather_facts_enabled": false,
            "gather_facts_method": null,
            "gather_facts_params": {},
            "change_secret_enabled": false,
            "change_secret_method": null,
            "change_secret_params": {},
            "push_account_enabled": false,
            "push_account_method": null,
            "push_account_params": {},
            "verify_account_enabled": false,
            "verify_account_method": null,
            "verify_account_params": {},
            "gather_accounts_enabled": false,
            "gather_accounts_method": null,
            "gather_accounts_params": {},
            "remove_account_enabled": false,
            "remove_account_method": null,
            "remove_account_params": {}
        },
        "protocols": [
            {
                "name": "http",
                "port": 80,
                "primary": true,
                "required": false,
                "default": false,
                "public": true,
                "setting": {
                    "username_selector": "name=username",
                    "password_selector": "name=password",
                    "submit_selector": "id=login_button",
                    "safe_mode": false
                }
            }
        ]
    },
    {
        "created_by": null,
        "updated_by": null,
        "comment": "",
        "name": "Vmware-vSphere",
        "category": "cloud",
        "type": "private",
        "meta": {},
        "internal": true,
        "domain_enabled": false,
        "su_enabled": false,
        "su_method": null,
        "custom_fields": [],
        "automation": {
            "ansible_enabled": false,
            "ansible_config": {},
            "ping_enabled": false,
            "ping_method": null,
            "ping_params": {},
            "gather_facts_enabled": false,
            "gather_facts_method": null,
            "gather_facts_params": {},
            "change_secret_enabled": false,
            "change_secret_method": null,
            "change_secret_params": {},
            "push_account_enabled": false,
            "push_account_method": null,
            "push_account_params": {},
            "verify_account_enabled": false,
            "verify_account_method": null,
            "verify_account_params": {},
            "gather_accounts_enabled": false,
            "gather_accounts_method": null,
            "gather_accounts_params": {},
            "remove_account_enabled": false,
            "remove_account_method": null,
            "remove_account_params": {}
        },
        "protocols": [
            {
                "name": "http",
                "port": 80,
                "primary": true,
                "required": false,
                "default": false,
                "public": true,
                "setting": {
                    "username_selector": "name=username",
                    "password_selector": "name=password",
                    "submit_selector": "id=login_button",
                    "safe_mode": false
                }
            }
        ]
    },
    {
        "created_by": null,
        "updated_by": null,
        "comment": "",
        "name": "Kubernetes",
        "category": "cloud",
        "type": "k8s",
        "meta": {},
        "internal": true,
        "domain_enabled": false,
        "su_enabled": false,
        "su_method": null,
        "custom_fields": [],
        "automation": {
            "ansible_enabled": false,
            "ansible_config": {},
            "ping_enabled": false,
            "ping_method": null,
            "ping_params": {},
            "gather_facts_enabled": false,
            "gather_facts_method": null,
            "gather_facts_params": {},
            "change_secret_enabled": false,
            "change_secret_method": null,
            "change_secret_params": {},
            "push_account_enabled": false,
            "push_account_method": null,
            "push_account_params": {},
            "verify_account_enabled": false,
            "verify_account_method": null,
            "verify_account_params": {},
            "gather_accounts_enabled": false,
            "gather_accounts_method": null,
            "gather_accounts_params": {},
            "remove_account_enabled": false,
            "remove_account_method": null,
            "remove_account_params": {}
        },
        "protocols": [
            {
                "name": "k8s",
                "port": 443,
                "primary": true,
                "required": false,
                "default": false,
                "public": true,
                "setting": {}
            }
        ]
    },
    {
        "created_by": "System",
        "updated_by": "System",
        "comment": "ChatGPT",
        "name": "ChatGPT",
        "category": "gpt",
        "type": "chatgpt",
        "meta": {},
        "internal": true,
        "domain_enabled": false,
        "su_enabled": false,
        "su_method": null,
        "custom_fields": [],
        "automation": {
            "ansible_enabled": false,
            "ansible_config": {},
            "ping_enabled": false,
            "ping_method": null,
            "ping_params": {},
            "gather_facts_enabled": false,
            "gather_facts_method": null,
            "gather_facts_params": {},
            "change_secret_enabled": false,
            "change_secret_method": null,
            "change_secret_params": {},
            "push_account_enabled": false,
            "push_account_method": null,
            "push_account_params": {},
            "verify_account_enabled": false,
            "verify_account_method": null,
            "verify_account_params": {},
            "gather_accounts_enabled": false,
            "gather_accounts_method": null,
            "gather_accounts_params": {},
            "remove_account_enabled": false,
            "remove_account_method": null,
            "remove_account_params": {}
        },
        "protocols": [
            {
                "name": "chatgpt",
                "port": 443,
                "primary": true,
                "required": false,
                "default": false,
                "public": true,
                "setting": {
                    "api_mode": "gpt-3.5-turbo"
                }
            }
        ]
    },
    {
        "created_by": "System",
        "updated_by": "System",
        "comment": "DB2",
        "name": "DB2",
        "category": "database",
        "type": "db2",
        "meta": {},
        "internal": true,
        "domain_enabled": true,
        "su_enabled": false,
        "su_method": null,
        "custom_fields": [],
        "automation": {
            "ansible_enabled": false,
            "ansible_config": {},
            "ping_enabled": false,
            "ping_method": null,
            "ping_params": {},
            "gather_facts_enabled": false,
            "gather_facts_method": null,
            "gather_facts_params": {},
            "change_secret_enabled": false,
            "change_secret_method": null,
            "change_secret_params": {},
            "push_account_enabled": false,
            "push_account_method": null,
            "push_account_params": {},
            "verify_account_enabled": false,
            "verify_account_method": null,
            "verify_account_params": {},
            "gather_accounts_enabled": false,
            "gather_accounts_method": null,
            "gather_accounts_params": {},
            "remove_account_enabled": false,
            "remove_account_method": null,
            "remove_account_params": {}
        },
        "protocols": [
            {
                "name": "db2",
                "port": 50000,
                "primary": true,
                "required": false,
                "default": false,
                "public": true,
                "setting": {}
            }
        ]
    },
    {
        "created_by": "System",
        "updated_by": "System",
        "comment": "Dameng",
        "name": "Dameng",
        "category": "database",
        "type": "dameng",
        "meta": {},
        "internal": true,
        "domain_enabled": true,
        "su_enabled": false,
        "su_method": null,
        "custom_fields": [],
        "automation": {
            "ansible_enabled": false,
            "ansible_config": {},
            "ping_enabled": false,
            "ping_method": null,
            "ping_params": {},
            "gather_facts_enabled": false,
            "gather_facts_method": null,
            "gather_facts_params": {},
            "change_secret_enabled": false,
            "change_secret_method": null,
            "change_secret_params": {},
            "push_account_enabled": false,
            "push_account_method": null,
            "push_account_params": {},
            "verify_account_enabled": false,
            "verify_account_method": null,
            "verify_account_params": {},
            "gather_accounts_enabled": false,
            "gather_accounts_method": null,
            "gather_accounts_params": {},
            "remove_account_enabled": false,
            "remove_account_method": null,
            "remove_account_params": {}
        },
        "protocols": [
            {
                "name": "dameng",
                "port": 5236,
                "primary": true,
                "required": false,
                "default": false,
                "public": true,
                "setting": {}
            }
        ]
    }
]'''


def create_internal_platforms(apps, *args):
    platform_cls = apps.get_model('assets', 'Platform')
    automation_cls = apps.get_model('assets', 'PlatformAutomation')
    platforms_data = json.loads(platforms_data_json)

    for platform_data in platforms_data:
        protocols = platform_data.pop('protocols', [])
        platform_data['protocols'] = [p for p in protocols if p.pop('primary', True) is not None]
        AllTypes.create_or_update_by_platform_data(platform_data, platform_cls=platform_cls,
                                                   automation_cls=automation_cls)


def migrate_automation_ansible_remove_account(apps, *args):
    automation_model = apps.get_model('assets', 'PlatformAutomation')
    automation_map = {
        ('oracle',): 'remove_account_oracle',
        ('windows',): 'remove_account_windows',
        ('mongodb',): 'remove_account_mongodb',
        ('linux', 'unix'): 'remove_account_posix',
        ('sqlserver',): 'remove_account_sqlserver',
        ('mysql', 'mariadb'): 'remove_account_mysql',
        ('postgresql',): 'remove_account_postgresql',
    }

    update_objs = []
    types = list(reduce(lambda x, y: x + y, automation_map.keys()))
    qs = automation_model.objects.filter(platform__type__in=types).annotate(tp=F('platform__type'))
    for automation in qs:
        for types, method in automation_map.items():
            if automation.tp in types:
                automation.remove_account_enabled = True
                automation.remove_account_method = method
                break
        update_objs.append(automation)
    automation_model.objects.bulk_update(update_objs, ['remove_account_enabled', 'remove_account_method'])


class Migration(migrations.Migration):
    dependencies = [
        ('assets', '0002_auto_20180105_1807'),
    ]

    operations = [
        migrations.RunPython(create_internal_platforms),
        migrations.RunPython(migrate_automation_ansible_remove_account),
    ]
