# Generated by Django 4.1.13 on 2025-03-13 09:14
import time
from django.db import migrations, models


def migrate_execution_type(apps, schema_editor):
    count = 0
    bulk_size = 1000
    while True:
        start = time.time()
        execution_model = apps.get_model('assets', 'AutomationExecution')
        execution_objs = []
        executions = execution_model.objects.all()[count:count + bulk_size]
        if not executions:
            break
        for execution in executions:
            snapshot = execution.snapshot
            execution.type = snapshot.get('type', '')
            execution_objs.append(execution)
        execution_model.objects.bulk_update(execution_objs, ['type'])
        print("\tUpdate rexecutions: {}-{} using: {:.2f}s".format(
            count, count + len(executions), time.time() - start
        ))
        count += len(executions)


class Migration(migrations.Migration):
    dependencies = [
        ('assets', '0014_alter_automationexecution_duration'),
    ]

    operations = [
        migrations.AddField(
            model_name='automationexecution',
            name='type',
            field=models.Char<PERSON>ield(default='', max_length=32, verbose_name='Type'),
        ),
        migrations.RunPython(migrate_execution_type)
    ]
