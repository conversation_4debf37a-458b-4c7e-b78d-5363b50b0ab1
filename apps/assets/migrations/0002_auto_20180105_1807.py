# Generated by Django 4.1.13 on 2024-05-09 03:16

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models
from django.db.models import F

import assets.models.asset.common


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ('assets', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='favoriteasset',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterUniqueTogether(
            name='domain',
            unique_together={('org_id', 'name')},
        ),
        migrations.AddField(
            model_name='baseautomation',
            name='assets',
            field=models.ManyToManyField(blank=True, to='assets.asset', verbose_name='Assets'),
        ),
        migrations.AddField(
            model_name='baseautomation',
            name='nodes',
            field=models.ManyToManyField(blank=True, to='assets.node', verbose_name='Node'),
        ),
        migrations.AddField(
            model_name='automationexecution',
            name='automation',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='executions',
                                    to='assets.baseautomation', verbose_name='Automation task'),
        ),
        migrations.AddField(
            model_name='asset',
            name='domain',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL,
                                    related_name='assets', to='assets.domain', verbose_name='Zone'),
        ),
        migrations.AddField(
            model_name='asset',
            name='nodes',
            field=models.ManyToManyField(default=assets.models.asset.common.default_node, related_name='assets',
                                         to='assets.node', verbose_name='Nodes'),
        ),
        migrations.AddField(
            model_name='asset',
            name='platform',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='assets',
                                    to='assets.platform', verbose_name='Platform'),
        ),
        migrations.CreateModel(
            name='AssetBaseAutomation',
            fields=[
            ],
            options={
                'verbose_name': 'Asset automation task',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('assets.baseautomation',),
        ),
        migrations.CreateModel(
            name='GatherFactsAutomation',
            fields=[
                ('baseautomation_ptr',
                 models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True,
                                      primary_key=True, serialize=False, to='assets.baseautomation')),
            ],
            options={
                'verbose_name': 'Gather asset facts',
            },
            bases=('assets.assetbaseautomation',),
        ),
        migrations.CreateModel(
            name='PingAutomation',
            fields=[
                ('baseautomation_ptr',
                 models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True,
                                      primary_key=True, serialize=False, to='assets.baseautomation')),
            ],
            options={
                'verbose_name': 'Ping asset',
            },
            bases=('assets.assetbaseautomation',),
        ),
        migrations.AlterUniqueTogether(
            name='favoriteasset',
            unique_together={('user', 'asset')},
        ),
        migrations.AlterUniqueTogether(
            name='baseautomation',
            unique_together={('org_id', 'name', 'type')},
        ),
        migrations.AlterUniqueTogether(
            name='asset',
            unique_together={('org_id', 'name')},
        ),
        migrations.CreateModel(
            name='Gateway',
            fields=[
            ],
            options={
                'verbose_name': 'Gateway',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('assets.host',),
        ),
    ]
