- hosts: oracle
  gather_facts: no
  vars:
    ansible_python_interpreter: "{{ local_python_interpreter }}"

  tasks:
    - name: Test Oracle connection
      oracle_ping:
        login_user: "{{ jms_account.username }}"
        login_password: "{{ jms_account.secret }}"
        login_host: "{{ jms_asset.address }}"
        login_port: "{{ jms_asset.port }}"
        login_database: "{{ jms_asset.spec_info.db_name }}"
        mode: "{{ jms_account.mode }}"
