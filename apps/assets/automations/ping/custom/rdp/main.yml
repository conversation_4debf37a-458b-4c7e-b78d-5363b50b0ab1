- hosts: custom
  gather_facts: no
  vars:
    ansible_shell_type: sh
    ansible_connection: local
    ansible_python_interpreter: /opt/py3/bin/python

  tasks:
    - name: Test asset connection (pyfreerdp)
      rdp_ping:
        login_user: "{{ jms_account.username }}"
        login_password: "{{ jms_account.secret }}"
        login_host: "{{ jms_asset.address }}"
        login_port: "{{ jms_asset.port }}"
        login_secret_type: "{{ jms_account.secret_type }}"
